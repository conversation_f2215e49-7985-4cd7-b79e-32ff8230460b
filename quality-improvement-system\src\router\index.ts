import { createRouter, createWebHistory } from 'vue-router'
import AdminLayout from '../components/Layout/AdminLayout.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: AdminLayout,
      redirect: '/dashboard',
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('../views/Dashboard.vue'),
          meta: { title: '首页' }
        },
        // 核心业务模块
        {
          path: 'problem-register',
          name: 'ProblemRegister',
          component: () => import('../views/core/ProblemRegister.vue'),
          meta: { title: '问题登记' }
        },
        {
          path: 'root-cause-analysis',
          name: 'RootCauseAnalysis',
          component: () => import('../views/core/RootCauseAnalysis.vue'),
          meta: { title: '根因分析' }
        },
        {
          path: 'action-plan',
          name: 'ActionPlan',
          component: () => import('../views/core/ActionPlan.vue'),
          meta: { title: '行动方案' }
        },
        {
          path: 'effect-verification',
          name: 'EffectVerification',
          component: () => import('../views/core/EffectVerification.vue'),
          meta: { title: '效果验证' }
        },
        {
          path: 'problem-closure',
          name: 'ProblemClosure',
          component: () => import('../views/core/ProblemClosure.vue'),
          meta: { title: '问题关闭' }
        },
        // 辅助管理模块
        {
          path: 'risk-warning',
          name: 'RiskWarning',
          component: () => import('../views/auxiliary/RiskWarning.vue'),
          meta: { title: '风险预警' }
        },
        {
          path: 'repeat-problem',
          name: 'RepeatProblem',
          component: () => import('../views/auxiliary/RepeatProblem.vue'),
          meta: { title: '重复问题管理' }
        },
        {
          path: 'knowledge-base',
          name: 'KnowledgeBase',
          component: () => import('../views/auxiliary/KnowledgeBase.vue'),
          meta: { title: '知识库管理' }
        },
        {
          path: 'reports-dashboard',
          name: 'ReportsDashboard',
          component: () => import('../views/auxiliary/ReportsDashboard.vue'),
          meta: { title: '报表与看板' }
        }
      ]
    }
  ],
})

export default router
