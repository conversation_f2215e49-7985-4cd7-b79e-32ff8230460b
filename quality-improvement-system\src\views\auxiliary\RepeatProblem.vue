<template>
  <div class="repeat-problem">
    <!-- TOP问题根因统计 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>TOP问题根因统计</span>
          </template>
          <div class="top-causes">
            <div v-for="(cause, index) in topCauses" :key="index" class="cause-item">
              <div class="cause-rank">{{ index + 1 }}</div>
              <div class="cause-content">
                <div class="cause-title">{{ cause.name }}</div>
                <div class="cause-stats">
                  <span class="cause-count">{{ cause.count }}次</span>
                  <span class="cause-percentage">{{ cause.percentage }}%</span>
                </div>
                <el-progress :percentage="cause.percentage" :show-text="false" />
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>问题类型分布</span>
          </template>
          <div ref="problemTypeChart" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 重复问题预警 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>重复问题预警</span>
          <el-button type="primary" @click="refreshAnalysis">
            <el-icon><Refresh /></el-icon>
            重新分析
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="问题ID">
            <el-input v-model="searchForm.problemId" placeholder="请输入问题ID" clearable />
          </el-form-item>
          <el-form-item label="相似度">
            <el-select v-model="searchForm.similarity" placeholder="请选择相似度" clearable>
              <el-option label="高度相似(>90%)" value="high" />
              <el-option label="中度相似(70-90%)" value="medium" />
              <el-option label="低度相似(50-70%)" value="low" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 重复问题列表 -->
      <el-table :data="repeatProblems" style="width: 100%" v-loading="loading">
        <el-table-column prop="problemId" label="问题ID" width="140" />
        <el-table-column prop="description" label="问题描述" min-width="200" />
        <el-table-column prop="similarity" label="相似度" width="100">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.similarity" 
              :color="getSimilarityColor(scope.row.similarity)"
              :show-text="true"
            />
          </template>
        </el-table-column>
        <el-table-column prop="relatedProblems" label="关联历史问题" width="150">
          <template #default="scope">
            <el-tag 
              v-for="problem in scope.row.relatedProblems" 
              :key="problem" 
              size="small" 
              style="margin: 2px;"
            >
              {{ problem }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="rootCause" label="可能根因" min-width="180" />
        <el-table-column prop="createTime" label="发现时间" width="180" />
        <el-table-column prop="status" label="处理状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="viewDetails(scope.row)">查看</el-button>
              <el-button size="small" type="primary" @click="linkSolution(scope.row)">关联方案</el-button>
              <el-button size="small" type="warning" @click="markAsNew(scope.row)">标记为新问题</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 历史解决方案库 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>历史解决方案库</span>
      </template>
      
      <el-table :data="solutionLibrary" style="width: 100%">
        <el-table-column prop="rootCause" label="根因类型" width="150" />
        <el-table-column prop="problemType" label="问题类型" width="120" />
        <el-table-column prop="solution" label="解决方案" min-width="250" />
        <el-table-column prop="successRate" label="成功率" width="100">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.successRate" 
              :color="getSuccessRateColor(scope.row.successRate)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="usageCount" label="使用次数" width="100" />
        <el-table-column prop="lastUsed" label="最后使用" width="180" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="applySolution(scope.row)">应用</el-button>
              <el-button size="small" type="primary" @click="editSolution(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 关联方案对话框 -->
    <el-dialog v-model="showLinkDialog" title="关联历史解决方案" width="800px">
      <el-form :model="linkForm" label-width="120px">
        <el-form-item label="当前问题">
          <el-input v-model="linkForm.currentProblem" readonly />
        </el-form-item>
        <el-form-item label="选择方案">
          <el-table 
            :data="availableSolutions" 
            @selection-change="handleSolutionSelection"
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="rootCause" label="根因类型" width="120" />
            <el-table-column prop="solution" label="解决方案" min-width="200" />
            <el-table-column prop="successRate" label="成功率" width="100">
              <template #default="scope">
                {{ scope.row.successRate }}%
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="应用说明">
          <el-input
            v-model="linkForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请说明如何应用此方案到当前问题"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showLinkDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmLink">确定关联</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const showLinkDialog = ref(false)
const problemTypeChart = ref()

// 搜索表单
const searchForm = reactive({
  problemId: '',
  similarity: ''
})

// 关联表单
const linkForm = reactive({
  currentProblem: '',
  selectedSolutions: [],
  notes: ''
})

// TOP问题根因数据
const topCauses = ref([
  {
    name: '培训覆盖不足',
    count: 15,
    percentage: 42
  },
  {
    name: '设备维护不当',
    count: 12,
    percentage: 33
  },
  {
    name: '工艺参数偏差',
    count: 6,
    percentage: 17
  },
  {
    name: '材料质量问题',
    count: 3,
    percentage: 8
  }
])

// 重复问题数据
const repeatProblems = ref([
  {
    problemId: 'LOP20250101008',
    description: '螺母孔径偏大（实测7.08mm, 标准7±0.05mm,偏大0.03mm）',
    similarity: 95,
    relatedProblems: ['LOP20250101001', 'LOP20241215003'],
    rootCause: '设备偏移→校准失效→点检漏项',
    createTime: '2025-01-02 09:30:00',
    status: '待处理'
  },
  {
    problemId: 'LOP20250101009',
    description: '焊接点虚焊导致电阻异常',
    similarity: 88,
    relatedProblems: ['LOP20250101002', 'LOP20241220005'],
    rootCause: '焊接温度设定不当，操作员培训不足',
    createTime: '2025-01-02 10:15:00',
    status: '已关联方案'
  },
  {
    problemId: 'LOP20250101010',
    description: '包装标签位置偏移',
    similarity: 75,
    relatedProblems: ['LOP20250101003'],
    rootCause: '操作标准不明确，培训不到位',
    createTime: '2025-01-02 11:00:00',
    status: '待处理'
  },
  {
    problemId: 'LOP20250101011',
    description: '设备温度控制不稳定',
    similarity: 92,
    relatedProblems: ['LOP20250101004', 'LOP20241201008'],
    rootCause: '温控传感器老化，PID参数未优化',
    createTime: '2025-01-02 14:20:00',
    status: '已关联方案'
  },
  {
    problemId: 'LOP20250101012',
    description: '操作员未按新版SOP执行',
    similarity: 85,
    relatedProblems: ['LOP20250101005'],
    rootCause: '新版SOP培训覆盖不足',
    createTime: '2025-01-02 15:45:00',
    status: '标记为新问题'
  }
])

// 解决方案库数据
const solutionLibrary = ref([
  {
    rootCause: '培训覆盖不足',
    problemType: '操作失误',
    solution: '制定标准化培训计划，建立培训考核机制，定期复训',
    successRate: 85,
    usageCount: 12,
    lastUsed: '2025-01-01 16:30:00'
  },
  {
    rootCause: '设备维护不当',
    problemType: '设备故障',
    solution: '优化设备点检计划，建立预防性维护体系',
    successRate: 92,
    usageCount: 8,
    lastUsed: '2024-12-28 14:20:00'
  },
  {
    rootCause: '工艺参数偏差',
    problemType: '工艺问题',
    solution: '建立工艺参数监控系统，设置自动报警机制',
    successRate: 78,
    usageCount: 6,
    lastUsed: '2024-12-25 11:15:00'
  },
  {
    rootCause: '材料质量问题',
    problemType: '材料缺陷',
    solution: '加强供应商质量管理，建立来料检验标准',
    successRate: 88,
    usageCount: 4,
    lastUsed: '2024-12-20 09:45:00'
  }
])

// 可用方案数据
const availableSolutions = ref([])

// 方法
const getSimilarityColor = (similarity: number) => {
  if (similarity >= 90) return '#f56c6c'
  if (similarity >= 70) return '#e6a23c'
  return '#909399'
}

const getSuccessRateColor = (rate: number) => {
  if (rate >= 80) return '#67c23a'
  if (rate >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getStatusType = (status: string) => {
  switch (status) {
    case '待处理': return 'warning'
    case '已关联方案': return 'success'
    case '标记为新问题': return 'info'
    default: return ''
  }
}

const handleSearch = () => {
  ElMessage.success('搜索功能演示')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    problemId: '',
    similarity: ''
  })
}

const refreshAnalysis = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('重复问题分析已完成')
  }, 2000)
}

const viewDetails = (row: any) => {
  ElMessage.info(`查看问题详情: ${row.problemId}`)
}

const linkSolution = (row: any) => {
  linkForm.currentProblem = `${row.problemId} - ${row.description}`
  availableSolutions.value = solutionLibrary.value.filter(solution => 
    row.rootCause.includes(solution.rootCause)
  )
  showLinkDialog.value = true
}

const markAsNew = (row: any) => {
  ElMessageBox.confirm(
    `确定要将问题 ${row.problemId} 标记为新问题吗？`,
    '确认标记',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('已标记为新问题')
  })
}

const applySolution = (row: any) => {
  ElMessage.success(`应用解决方案: ${row.solution}`)
}

const editSolution = (row: any) => {
  ElMessage.info(`编辑解决方案: ${row.solution}`)
}

const handleSolutionSelection = (selection: any[]) => {
  linkForm.selectedSolutions = selection
}

const confirmLink = () => {
  if (linkForm.selectedSolutions.length === 0) {
    ElMessage.warning('请选择至少一个解决方案')
    return
  }
  ElMessage.success('方案关联成功')
  showLinkDialog.value = false
  Object.assign(linkForm, {
    currentProblem: '',
    selectedSolutions: [],
    notes: ''
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const initChart = () => {
  nextTick(() => {
    if (problemTypeChart.value) {
      const chart = echarts.init(problemTypeChart.value)
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '问题类型',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 15, name: '设备故障' },
              { value: 12, name: '工艺问题' },
              { value: 8, name: '人员操作' },
              { value: 6, name: '材料缺陷' },
              { value: 4, name: '环境因素' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      chart.setOption(option)
    }
  })
}

onMounted(() => {
  total.value = repeatProblems.value.length
  initChart()
})
</script>

<style scoped>
.repeat-problem {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.top-causes {
  padding: 10px 0;
}

.cause-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.cause-rank {
  width: 40px;
  height: 40px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  margin-right: 15px;
}

.cause-content {
  flex: 1;
}

.cause-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #303133;
}

.cause-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 8px;
}

.cause-count {
  color: #409eff;
  font-weight: bold;
}

.cause-percentage {
  color: #67c23a;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
