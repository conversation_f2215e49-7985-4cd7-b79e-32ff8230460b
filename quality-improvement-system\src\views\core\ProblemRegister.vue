<template>
  <div class="problem-register">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>问题登记</span>
          <el-button type="primary" @click="showAddDialog = true">
            <el-icon><Plus /></el-icon>
            新增问题
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="问题ID">
            <el-input v-model="searchForm.id" placeholder="请输入问题ID" clearable />
          </el-form-item>
          <el-form-item label="风险等级">
            <el-select v-model="searchForm.riskLevel" placeholder="请选择风险等级" clearable>
              <el-option label="高危" value="高危" />
              <el-option label="中危" value="中危" />
              <el-option label="低危" value="低危" />
            </el-select>
          </el-form-item>
          <el-form-item label="责任区域">
            <el-select v-model="searchForm.area" placeholder="请选择责任区域" clearable>
              <el-option label="生产线A" value="生产线A" />
              <el-option label="生产线B" value="生产线B" />
              <el-option label="品保部" value="品保部" />
              <el-option label="设备部" value="设备部" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 问题列表 -->
      <el-table :data="problemList" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="问题ID" width="140" />
        <el-table-column prop="description" label="问题描述" min-width="200" />
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskLevelType(scope.row.riskLevel)">
              {{ scope.row.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="area" label="责任区域" width="120" />
        <el-table-column prop="problemType" label="问题类型" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="viewProblem(scope.row)">查看</el-button>
              <el-button size="small" type="primary" @click="editProblem(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteProblem(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑问题' : '新增问题'"
      width="800px"
      @close="resetForm"
    >
      <el-form :model="problemForm" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="问题描述" prop="description">
              <el-input
                v-model="problemForm.description"
                type="textarea"
                :rows="3"
                placeholder="请详细描述问题现象和量化数据"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发生时间" prop="occurTime">
              <el-date-picker
                v-model="problemForm.occurTime"
                type="datetime"
                placeholder="选择发生时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="责任区域" prop="area">
              <el-select v-model="problemForm.area" placeholder="请选择责任区域" style="width: 100%">
                <el-option label="生产线A" value="生产线A" />
                <el-option label="生产线B" value="生产线B" />
                <el-option label="品保部" value="品保部" />
                <el-option label="设备部" value="设备部" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="问题类型" prop="problemType">
              <el-select v-model="problemForm.problemType" placeholder="请选择问题类型" style="width: 100%">
                <el-option label="设备故障" value="设备故障" />
                <el-option label="工艺问题" value="工艺问题" />
                <el-option label="人员操作失误" value="人员操作失误" />
                <el-option label="材料缺陷" value="材料缺陷" />
                <el-option label="环境因素" value="环境因素" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="风险等级" prop="riskLevel">
              <el-select v-model="problemForm.riskLevel" placeholder="请选择风险等级" style="width: 100%">
                <el-option label="高危" value="高危">
                  <span style="color: #f56c6c">▲ 高危 - 72小时内解决</span>
                </el-option>
                <el-option label="中危" value="中危">
                  <span style="color: #e6a23c">● 中危 - 周级解决</span>
                </el-option>
                <el-option label="低危" value="低危">
                  <span style="color: #909399">◆ 低危 - 月度解决</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报告人" prop="reporter">
              <el-input v-model="problemForm.reporter" placeholder="请输入报告人姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="缺陷照片/视频">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            multiple
            :auto-upload="false"
            :file-list="fileList"
            :on-change="handleFileChange"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持jpg/png/mp4文件，且不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showAddDialog = ref(false)
const isEdit = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref<FormInstance>()
const fileList = ref<UploadFile[]>([])

// 搜索表单
const searchForm = reactive({
  id: '',
  riskLevel: '',
  area: ''
})

// 问题表单
const problemForm = reactive({
  id: '',
  description: '',
  occurTime: '',
  area: '',
  problemType: '',
  riskLevel: '',
  reporter: ''
})

// 表单验证规则
const rules: FormRules = {
  description: [
    { required: true, message: '请输入问题描述', trigger: 'blur' }
  ],
  occurTime: [
    { required: true, message: '请选择发生时间', trigger: 'change' }
  ],
  area: [
    { required: true, message: '请选择责任区域', trigger: 'change' }
  ],
  problemType: [
    { required: true, message: '请选择问题类型', trigger: 'change' }
  ],
  riskLevel: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  reporter: [
    { required: true, message: '请输入报告人', trigger: 'blur' }
  ]
}

// 问题列表数据
const problemList = ref([
  {
    id: 'LOP20250101001',
    description: '螺母孔径偏小（实测6.93mm, 标准7±0.05mm,偏小0.02mm）',
    riskLevel: '高危',
    area: '生产线A',
    problemType: '工艺问题',
    createTime: '2025-01-01 09:30:00',
    status: '待分析',
    reporter: '张三'
  },
  {
    id: 'LOP20250101002',
    description: '焊接点虚焊导致接触不良',
    riskLevel: '中危',
    area: '生产线B',
    problemType: '设备故障',
    createTime: '2025-01-01 10:15:00',
    status: '执行中',
    reporter: '李四'
  },
  {
    id: 'LOP20250101003',
    description: '包装标签贴歪',
    riskLevel: '低危',
    area: '品保部',
    problemType: '人员操作失误',
    createTime: '2025-01-01 11:00:00',
    status: '待关闭',
    reporter: '王五'
  },
  {
    id: 'LOP20250101004',
    description: '设备温度异常波动',
    riskLevel: '高危',
    area: '设备部',
    problemType: '设备故障',
    createTime: '2025-01-01 14:20:00',
    status: '待制定方案',
    reporter: '赵六'
  },
  {
    id: 'LOP20250101005',
    description: '操作员未按SOP执行',
    riskLevel: '中危',
    area: '生产线A',
    problemType: '人员操作失误',
    createTime: '2025-01-01 15:45:00',
    status: '已关闭',
    reporter: '钱七'
  }
])

// 方法
const getRiskLevelType = (level: string) => {
  switch (level) {
    case '高危': return 'danger'
    case '中危': return 'warning'
    case '低危': return 'info'
    default: return ''
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case '待分析': return 'warning'
    case '执行中': return 'primary'
    case '待关闭': return 'success'
    case '已关闭': return 'info'
    default: return ''
  }
}

const handleSearch = () => {
  // 搜索逻辑
  ElMessage.success('搜索功能演示')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    id: '',
    riskLevel: '',
    area: ''
  })
}

const viewProblem = (row: any) => {
  ElMessage.info(`查看问题: ${row.id}`)
}

const editProblem = (row: any) => {
  isEdit.value = true
  Object.assign(problemForm, row)
  showAddDialog.value = true
}

const deleteProblem = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除问题 ${row.id} 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleFileChange = (file: UploadFile, fileList: UploadFile[]) => {
  // 文件上传处理
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  isEdit.value = false
  fileList.value = []
  Object.assign(problemForm, {
    id: '',
    description: '',
    occurTime: '',
    area: '',
    problemType: '',
    riskLevel: '',
    reporter: ''
  })
}

const submitForm = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        ElMessage.success('编辑成功')
      } else {
        // 生成新的问题ID
        const now = new Date()
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '')
        const timeStr = String(Date.now()).slice(-3)
        problemForm.id = `LOP${dateStr}${timeStr}`
        ElMessage.success('新增成功')
      }
      showAddDialog.value = false
      resetForm()
    }
  })
}

onMounted(() => {
  total.value = problemList.value.length
})
</script>

<style scoped>
.problem-register {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.upload-demo {
  width: 100%;
}
</style>
