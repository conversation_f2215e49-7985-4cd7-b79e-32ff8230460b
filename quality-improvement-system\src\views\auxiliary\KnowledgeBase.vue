<template>
  <div class="knowledge-base">
    <!-- 知识库统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalCases }}</div>
              <div class="stat-label">经典案例</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalLessons }}</div>
              <div class="stat-label">失败教训</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon info">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalCategories }}</div>
              <div class="stat-label">知识分类</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon primary">
              <el-icon><View /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.totalViews }}</div>
              <div class="stat-label">总浏览量</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 知识库内容 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>知识库管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="showAddDialog = true">
              <el-icon><Plus /></el-icon>
              新增知识
            </el-button>
            <el-button @click="exportKnowledge">
              <el-icon><Download /></el-icon>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索和筛选 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input 
              v-model="searchForm.keyword" 
              placeholder="请输入关键词搜索" 
              clearable 
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item label="知识类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
              <el-option label="经典案例" value="success" />
              <el-option label="失败教训" value="failure" />
            </el-select>
          </el-form-item>
          <el-form-item label="知识分类">
            <el-select v-model="searchForm.category" placeholder="请选择分类" clearable>
              <el-option label="设备维护" value="设备维护" />
              <el-option label="工艺改进" value="工艺改进" />
              <el-option label="人员培训" value="人员培训" />
              <el-option label="质量控制" value="质量控制" />
              <el-option label="供应商管理" value="供应商管理" />
              <el-option label="环境控制" value="环境控制" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 知识列表 -->
      <el-table :data="knowledgeList" style="width: 100%" v-loading="loading">
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === 'success' ? 'success' : 'warning'">
              {{ scope.row.type === 'success' ? '经典案例' : '失败教训' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" min-width="200" />
        <el-table-column prop="category" label="分类" width="120">
          <template #default="scope">
            <el-tag size="small">{{ scope.row.category }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="tags" label="标签" width="150">
          <template #default="scope">
            <el-tag 
              v-for="tag in scope.row.tags" 
              :key="tag" 
              size="small" 
              style="margin: 2px;"
            >
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="author" label="创建人" width="100" />
        <el-table-column prop="viewCount" label="浏览量" width="80" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="version" label="版本" width="80" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="viewKnowledge(scope.row)">查看</el-button>
              <el-button size="small" type="primary" @click="editKnowledge(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteKnowledge(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑知识对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="isEdit ? '编辑知识' : '新增知识'"
      width="1000px"
      @close="resetForm"
    >
      <el-form :model="knowledgeForm" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="知识类型" prop="type">
              <el-radio-group v-model="knowledgeForm.type">
                <el-radio label="success">经典案例</el-radio>
                <el-radio label="failure">失败教训</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="知识分类" prop="category">
              <el-select v-model="knowledgeForm.category" placeholder="请选择分类" style="width: 100%">
                <el-option label="设备维护" value="设备维护" />
                <el-option label="工艺改进" value="工艺改进" />
                <el-option label="人员培训" value="人员培训" />
                <el-option label="质量控制" value="质量控制" />
                <el-option label="供应商管理" value="供应商管理" />
                <el-option label="环境控制" value="环境控制" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="标题" prop="title">
          <el-input v-model="knowledgeForm.title" placeholder="请输入知识标题" />
        </el-form-item>

        <el-form-item label="问题描述" prop="problemDescription">
          <el-input
            v-model="knowledgeForm.problemDescription"
            type="textarea"
            :rows="3"
            placeholder="请详细描述遇到的问题"
          />
        </el-form-item>

        <el-form-item label="根本原因" prop="rootCause">
          <el-input
            v-model="knowledgeForm.rootCause"
            type="textarea"
            :rows="3"
            placeholder="请描述问题的根本原因"
          />
        </el-form-item>

        <el-form-item label="解决方案" prop="solution" v-if="knowledgeForm.type === 'success'">
          <el-input
            v-model="knowledgeForm.solution"
            type="textarea"
            :rows="4"
            placeholder="请详细描述成功的解决方案"
          />
        </el-form-item>

        <el-form-item label="失败原因" prop="failureReason" v-if="knowledgeForm.type === 'failure'">
          <el-input
            v-model="knowledgeForm.failureReason"
            type="textarea"
            :rows="4"
            placeholder="请详细描述失败的原因和教训"
          />
        </el-form-item>

        <el-form-item label="效果验证" prop="verification" v-if="knowledgeForm.type === 'success'">
          <el-input
            v-model="knowledgeForm.verification"
            type="textarea"
            :rows="3"
            placeholder="请描述效果验证的方法和结果"
          />
        </el-form-item>

        <el-form-item label="预防措施" prop="prevention">
          <el-input
            v-model="knowledgeForm.prevention"
            type="textarea"
            :rows="3"
            placeholder="请描述预防类似问题的措施"
          />
        </el-form-item>

        <el-form-item label="关键要点">
          <el-input
            v-model="knowledgeForm.keyPoints"
            type="textarea"
            :rows="3"
            placeholder="请总结关键要点和注意事项"
          />
        </el-form-item>

        <el-form-item label="标签">
          <el-select
            v-model="knowledgeForm.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option label="紧急" value="紧急" />
            <el-option label="常见" value="常见" />
            <el-option label="复杂" value="复杂" />
            <el-option label="简单" value="简单" />
            <el-option label="重要" value="重要" />
          </el-select>
        </el-form-item>

        <el-form-item label="相关文档">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            multiple
            :auto-upload="false"
            :file-list="fileList"
            :on-change="handleFileChange"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持上传相关的SOP、图片、视频等文档
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 知识详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="知识详情" width="800px">
      <div v-if="currentKnowledge" class="knowledge-detail">
        <h3>{{ currentKnowledge.title }}</h3>
        <div class="detail-meta">
          <el-tag :type="currentKnowledge.type === 'success' ? 'success' : 'warning'">
            {{ currentKnowledge.type === 'success' ? '经典案例' : '失败教训' }}
          </el-tag>
          <el-tag size="small">{{ currentKnowledge.category }}</el-tag>
          <span class="meta-info">创建人：{{ currentKnowledge.author }}</span>
          <span class="meta-info">浏览量：{{ currentKnowledge.viewCount }}</span>
        </div>
        
        <div class="detail-section">
          <h4>问题描述</h4>
          <p>{{ currentKnowledge.problemDescription }}</p>
        </div>
        
        <div class="detail-section">
          <h4>根本原因</h4>
          <p>{{ currentKnowledge.rootCause }}</p>
        </div>
        
        <div class="detail-section" v-if="currentKnowledge.solution">
          <h4>解决方案</h4>
          <p>{{ currentKnowledge.solution }}</p>
        </div>
        
        <div class="detail-section" v-if="currentKnowledge.failureReason">
          <h4>失败原因</h4>
          <p>{{ currentKnowledge.failureReason }}</p>
        </div>
        
        <div class="detail-section" v-if="currentKnowledge.verification">
          <h4>效果验证</h4>
          <p>{{ currentKnowledge.verification }}</p>
        </div>
        
        <div class="detail-section">
          <h4>预防措施</h4>
          <p>{{ currentKnowledge.prevention }}</p>
        </div>
        
        <div class="detail-section" v-if="currentKnowledge.keyPoints">
          <h4>关键要点</h4>
          <p>{{ currentKnowledge.keyPoints }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const isEdit = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref<FormInstance>()
const fileList = ref<UploadFile[]>([])
const currentKnowledge = ref(null)

// 统计数据
const stats = ref({
  totalCases: 28,
  totalLessons: 12,
  totalCategories: 6,
  totalViews: 1256
})

// 搜索表单
const searchForm = reactive({
  keyword: '',
  type: '',
  category: ''
})

// 知识表单
const knowledgeForm = reactive({
  type: 'success',
  category: '',
  title: '',
  problemDescription: '',
  rootCause: '',
  solution: '',
  failureReason: '',
  verification: '',
  prevention: '',
  keyPoints: '',
  tags: []
})

// 表单验证规则
const rules: FormRules = {
  type: [
    { required: true, message: '请选择知识类型', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择知识分类', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  problemDescription: [
    { required: true, message: '请输入问题描述', trigger: 'blur' }
  ],
  rootCause: [
    { required: true, message: '请输入根本原因', trigger: 'blur' }
  ],
  solution: [
    { required: true, message: '请输入解决方案', trigger: 'blur' }
  ],
  failureReason: [
    { required: true, message: '请输入失败原因', trigger: 'blur' }
  ],
  prevention: [
    { required: true, message: '请输入预防措施', trigger: 'blur' }
  ]
}

// 知识库数据
const knowledgeList = ref([
  {
    id: 1,
    type: 'success',
    title: '螺母孔径偏差问题解决方案',
    category: '设备维护',
    tags: ['常见', '重要'],
    author: '张工程师',
    viewCount: 156,
    createTime: '2025-01-01 10:30:00',
    version: 'v1.2',
    problemDescription: '螺母孔径偏小（实测6.93mm, 标准7±0.05mm,偏小0.02mm）',
    rootCause: '设备偏移→校准失效→点检漏项',
    solution: '重新校准设备，优化点检计划，建立预防性维护体系',
    verification: '连续监测7天，孔径稳定在7±0.02mm范围内',
    prevention: '每周进行设备校准检查，建立设备偏移预警机制'
  },
  {
    id: 2,
    type: 'failure',
    title: '焊接温度控制失败教训',
    category: '工艺改进',
    tags: ['复杂', '紧急'],
    author: '李工程师',
    viewCount: 89,
    createTime: '2025-01-01 14:20:00',
    version: 'v1.0',
    problemDescription: '焊接点虚焊导致接触不良',
    rootCause: '焊接温度设定不当，操作员培训不足',
    failureReason: '仅调整温度参数，未考虑操作员技能差异，导致问题反复出现',
    prevention: '建立标准化培训体系，定期技能考核，温度参数与操作员技能匹配'
  },
  {
    id: 3,
    type: 'success',
    title: '包装标签贴附标准化改进',
    category: '人员培训',
    tags: ['简单', '常见'],
    author: '王工程师',
    viewCount: 234,
    createTime: '2025-01-01 16:45:00',
    version: 'v1.1',
    problemDescription: '包装标签贴歪，影响产品外观',
    rootCause: '操作标准不明确，培训不到位',
    solution: '制定详细的标签贴附SOP，增加可视化培训材料',
    verification: '标签贴附合格率从85%提升到98%',
    prevention: '定期培训复习，建立自检机制'
  },
  {
    id: 4,
    type: 'success',
    title: '设备温控系统优化案例',
    category: '设备维护',
    tags: ['重要', '复杂'],
    author: '赵工程师',
    viewCount: 178,
    createTime: '2025-01-01 18:30:00',
    version: 'v2.0',
    problemDescription: '设备温度异常波动，影响产品质量',
    rootCause: '温控传感器老化，PID参数未优化',
    solution: '更换传感器，重新调试PID参数，建立温度监控系统',
    verification: '温度波动从±3°C降低到±0.5°C',
    prevention: '建立传感器定期更换计划，温度数据实时监控'
  },
  {
    id: 5,
    type: 'failure',
    title: 'SOP执行监督机制失效教训',
    category: '人员培训',
    tags: ['重要', '常见'],
    author: '钱工程师',
    viewCount: 145,
    createTime: '2025-01-01 20:15:00',
    version: 'v1.0',
    problemDescription: '操作员未按SOP执行，导致质量问题',
    rootCause: '新版SOP培训覆盖不足，监督机制不完善',
    failureReason: '仅依靠口头培训，缺乏实际操作验证和持续监督',
    prevention: '建立培训考核机制，现场监督检查，定期技能评估'
  }
])

// 方法
const handleSearch = () => {
  ElMessage.success('搜索功能演示')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    keyword: '',
    type: '',
    category: ''
  })
}

const exportKnowledge = () => {
  ElMessage.success('知识库导出功能演示')
}

const viewKnowledge = (row: any) => {
  currentKnowledge.value = row
  // 增加浏览量
  row.viewCount++
  showDetailDialog.value = true
}

const editKnowledge = (row: any) => {
  isEdit.value = true
  Object.assign(knowledgeForm, row)
  showAddDialog.value = true
}

const deleteKnowledge = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除知识 "${row.title}" 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
  })
}

const handleFileChange = (file: UploadFile, fileList: UploadFile[]) => {
  // 文件上传处理
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  isEdit.value = false
  fileList.value = []
  Object.assign(knowledgeForm, {
    type: 'success',
    category: '',
    title: '',
    problemDescription: '',
    rootCause: '',
    solution: '',
    failureReason: '',
    verification: '',
    prevention: '',
    keyPoints: '',
    tags: []
  })
}

const submitForm = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        ElMessage.success('编辑成功')
      } else {
        ElMessage.success('新增成功')
      }
      showAddDialog.value = false
      resetForm()
    }
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

onMounted(() => {
  total.value = knowledgeList.value.length
})
</script>

<style scoped>
.knowledge-base {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.success {
  background-color: #67c23a;
}

.stat-icon.warning {
  background-color: #e6a23c;
}

.stat-icon.info {
  background-color: #909399;
}

.stat-icon.primary {
  background-color: #409eff;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.upload-demo {
  width: 100%;
}

.knowledge-detail {
  padding: 20px 0;
}

.knowledge-detail h3 {
  margin: 0 0 15px 0;
  color: #303133;
}

.detail-meta {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.meta-info {
  color: #909399;
  font-size: 14px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin: 0 0 10px 0;
  color: #409eff;
  font-size: 16px;
}

.detail-section p {
  margin: 0;
  line-height: 1.6;
  color: #606266;
}
</style>
