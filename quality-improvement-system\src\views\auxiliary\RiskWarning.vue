<template>
  <div class="risk-warning">
    <!-- 三色预警看板 -->
    <el-row :gutter="20" class="warning-cards">
      <el-col :span="8">
        <el-card class="warning-card red-card" @click="filterByRisk('red')">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">红色预警</div>
              <div class="card-subtitle">超期或高危问题</div>
              <div class="card-number">{{ redWarnings.length }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="warning-card yellow-card" @click="filterByRisk('yellow')">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">黄色预警</div>
              <div class="card-subtitle">即将超期或中危问题</div>
              <div class="card-number">{{ yellowWarnings.length }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="warning-card green-card" @click="filterByRisk('green')">
          <div class="card-content">
            <div class="card-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">绿色正常</div>
              <div class="card-subtitle">正常进行或低危问题</div>
              <div class="card-number">{{ greenWarnings.length }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 预警详情 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>预警详情</span>
          <div class="header-actions">
            <el-button-group>
              <el-button 
                :type="currentFilter === 'all' ? 'primary' : ''" 
                @click="filterByRisk('all')"
              >
                全部
              </el-button>
              <el-button 
                :type="currentFilter === 'red' ? 'danger' : ''" 
                @click="filterByRisk('red')"
              >
                红色预警
              </el-button>
              <el-button 
                :type="currentFilter === 'yellow' ? 'warning' : ''" 
                @click="filterByRisk('yellow')"
              >
                黄色预警
              </el-button>
              <el-button 
                :type="currentFilter === 'green' ? 'success' : ''" 
                @click="filterByRisk('green')"
              >
                绿色正常
              </el-button>
            </el-button-group>
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="问题ID">
            <el-input v-model="searchForm.problemId" placeholder="请输入问题ID" clearable />
          </el-form-item>
          <el-form-item label="责任区域">
            <el-select v-model="searchForm.area" placeholder="请选择责任区域" clearable>
              <el-option label="生产线A" value="生产线A" />
              <el-option label="生产线B" value="生产线B" />
              <el-option label="品保部" value="品保部" />
              <el-option label="设备部" value="设备部" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 预警列表 -->
      <el-table :data="filteredWarnings" style="width: 100%" v-loading="loading">
        <el-table-column prop="riskLevel" label="预警等级" width="100">
          <template #default="scope">
            <div class="risk-indicator">
              <span :class="getRiskClass(scope.row.riskLevel)">
                {{ getRiskSymbol(scope.row.riskLevel) }}
              </span>
              {{ scope.row.riskLevel }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="problemId" label="问题ID" width="140" />
        <el-table-column prop="description" label="问题描述" min-width="200" />
        <el-table-column prop="area" label="责任区域" width="120" />
        <el-table-column prop="responsible" label="责任人" width="100" />
        <el-table-column prop="deadline" label="截止时间" width="180" />
        <el-table-column prop="remainingTime" label="剩余时间" width="120">
          <template #default="scope">
            <span :class="getTimeClass(scope.row.remainingTime)">
              {{ scope.row.remainingTime }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="当前状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="viewProblem(scope.row)">查看</el-button>
              <el-button size="small" type="warning" @click="sendReminder(scope.row)">催办</el-button>
              <el-button size="small" type="danger" @click="escalate(scope.row)">上报</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 预警设置 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>预警设置</span>
      </template>
      <el-form :model="warningSettings" label-width="150px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="高危问题期限">
              <el-input-number v-model="warningSettings.highRiskHours" :min="1" :max="168" />
              <span style="margin-left: 10px;">小时</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="中危问题期限">
              <el-input-number v-model="warningSettings.mediumRiskDays" :min="1" :max="30" />
              <span style="margin-left: 10px;">天</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="低危问题期限">
              <el-input-number v-model="warningSettings.lowRiskDays" :min="1" :max="90" />
              <span style="margin-left: 10px;">天</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="提前预警时间">
              <el-input-number v-model="warningSettings.advanceWarningHours" :min="1" :max="72" />
              <span style="margin-left: 10px;">小时</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="自动上报级别">
              <el-select v-model="warningSettings.autoEscalateLevel" placeholder="选择自动上报级别">
                <el-option label="部门主管" value="department" />
                <el-option label="厂长" value="factory" />
                <el-option label="总经理" value="general" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="saveSettings">保存设置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 催办对话框 -->
    <el-dialog v-model="showReminderDialog" title="发送催办通知" width="500px">
      <el-form :model="reminderForm" label-width="100px">
        <el-form-item label="催办对象">
          <el-input v-model="reminderForm.target" readonly />
        </el-form-item>
        <el-form-item label="催办内容">
          <el-input
            v-model="reminderForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入催办内容"
          />
        </el-form-item>
        <el-form-item label="通知方式">
          <el-checkbox-group v-model="reminderForm.methods">
            <el-checkbox label="邮件">邮件</el-checkbox>
            <el-checkbox label="钉钉">钉钉</el-checkbox>
            <el-checkbox label="企业微信">企业微信</el-checkbox>
            <el-checkbox label="短信">短信</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showReminderDialog = false">取消</el-button>
          <el-button type="primary" @click="sendReminderNotification">发送</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const loading = ref(false)
const currentFilter = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const showReminderDialog = ref(false)

// 搜索表单
const searchForm = reactive({
  problemId: '',
  area: ''
})

// 催办表单
const reminderForm = reactive({
  target: '',
  content: '',
  methods: ['邮件']
})

// 预警设置
const warningSettings = reactive({
  highRiskHours: 72,
  mediumRiskDays: 7,
  lowRiskDays: 30,
  advanceWarningHours: 24,
  autoEscalateLevel: 'department'
})

// 预警数据
const warningData = ref([
  {
    problemId: 'LOP20250101001',
    description: '螺母孔径偏小（实测6.93mm, 标准7±0.05mm,偏小0.02mm）',
    area: '生产线A',
    responsible: '张工程师',
    deadline: '2025-01-03 09:30:00',
    remainingTime: '已超期2小时',
    status: '待分析',
    riskLevel: '红色',
    createTime: '2025-01-01 09:30:00'
  },
  {
    problemId: 'LOP20250101002',
    description: '焊接点虚焊导致接触不良',
    area: '生产线B',
    responsible: '李工程师',
    deadline: '2025-01-08 10:15:00',
    remainingTime: '3天12小时',
    status: '执行中',
    riskLevel: '黄色',
    createTime: '2025-01-01 10:15:00'
  },
  {
    problemId: 'LOP20250101003',
    description: '包装标签贴歪',
    area: '品保部',
    responsible: '王工程师',
    deadline: '2025-01-31 11:00:00',
    remainingTime: '29天13小时',
    status: '待关闭',
    riskLevel: '绿色',
    createTime: '2025-01-01 11:00:00'
  },
  {
    problemId: 'LOP20250101004',
    description: '设备温度异常波动',
    area: '设备部',
    responsible: '赵工程师',
    deadline: '2025-01-04 14:20:00',
    remainingTime: '18小时',
    status: '待制定方案',
    riskLevel: '黄色',
    createTime: '2025-01-01 14:20:00'
  },
  {
    problemId: 'LOP20250101005',
    description: '操作员未按SOP执行',
    area: '生产线A',
    responsible: '钱工程师',
    deadline: '2025-01-02 15:45:00',
    remainingTime: '已超期6小时',
    status: '分析中',
    riskLevel: '红色',
    createTime: '2025-01-01 15:45:00'
  }
])

// 计算属性
const redWarnings = computed(() => 
  warningData.value.filter(item => item.riskLevel === '红色')
)

const yellowWarnings = computed(() => 
  warningData.value.filter(item => item.riskLevel === '黄色')
)

const greenWarnings = computed(() => 
  warningData.value.filter(item => item.riskLevel === '绿色')
)

const filteredWarnings = computed(() => {
  let filtered = warningData.value
  
  if (currentFilter.value !== 'all') {
    const filterMap = {
      'red': '红色',
      'yellow': '黄色',
      'green': '绿色'
    }
    filtered = filtered.filter(item => item.riskLevel === filterMap[currentFilter.value])
  }
  
  if (searchForm.problemId) {
    filtered = filtered.filter(item => 
      item.problemId.toLowerCase().includes(searchForm.problemId.toLowerCase())
    )
  }
  
  if (searchForm.area) {
    filtered = filtered.filter(item => item.area === searchForm.area)
  }
  
  return filtered
})

// 方法
const getRiskClass = (level: string) => {
  switch (level) {
    case '红色': return 'risk-red'
    case '黄色': return 'risk-yellow'
    case '绿色': return 'risk-green'
    default: return ''
  }
}

const getRiskSymbol = (level: string) => {
  switch (level) {
    case '红色': return '▲'
    case '黄色': return '●'
    case '绿色': return '◆'
    default: return ''
  }
}

const getTimeClass = (time: string) => {
  if (time.includes('已超期')) return 'time-overdue'
  if (time.includes('小时') && !time.includes('天')) return 'time-urgent'
  return 'time-normal'
}

const getStatusType = (status: string) => {
  switch (status) {
    case '待分析': return 'warning'
    case '执行中': return 'primary'
    case '待关闭': return 'success'
    case '已关闭': return 'info'
    default: return ''
  }
}

const filterByRisk = (risk: string) => {
  currentFilter.value = risk
  currentPage.value = 1
}

const handleSearch = () => {
  ElMessage.success('搜索功能演示')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    problemId: '',
    area: ''
  })
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

const viewProblem = (row: any) => {
  ElMessage.info(`查看问题: ${row.problemId}`)
}

const sendReminder = (row: any) => {
  reminderForm.target = row.responsible
  reminderForm.content = `您负责的问题 ${row.problemId} 即将超期，请及时处理。`
  showReminderDialog.value = true
}

const escalate = (row: any) => {
  ElMessageBox.confirm(
    `确定要将问题 ${row.problemId} 上报给上级吗？`,
    '确认上报',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('上报成功')
  })
}

const sendReminderNotification = () => {
  ElMessage.success('催办通知已发送')
  showReminderDialog.value = false
  Object.assign(reminderForm, {
    target: '',
    content: '',
    methods: ['邮件']
  })
}

const saveSettings = () => {
  ElMessage.success('预警设置已保存')
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

onMounted(() => {
  total.value = warningData.value.length
})
</script>

<style scoped>
.risk-warning {
  padding: 20px;
}

.warning-cards {
  margin-bottom: 20px;
}

.warning-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 120px;
}

.warning-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.red-card {
  border-left: 4px solid #f56c6c;
}

.yellow-card {
  border-left: 4px solid #e6a23c;
}

.green-card {
  border-left: 4px solid #67c23a;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  font-size: 40px;
  margin-right: 20px;
}

.red-card .card-icon {
  color: #f56c6c;
}

.yellow-card .card-icon {
  color: #e6a23c;
}

.green-card .card-icon {
  color: #67c23a;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-subtitle {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.card-number {
  font-size: 32px;
  font-weight: bold;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.risk-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.risk-red {
  color: #f56c6c;
  font-weight: bold;
}

.risk-yellow {
  color: #e6a23c;
  font-weight: bold;
}

.risk-green {
  color: #67c23a;
  font-weight: bold;
}

.time-overdue {
  color: #f56c6c;
  font-weight: bold;
}

.time-urgent {
  color: #e6a23c;
  font-weight: bold;
}

.time-normal {
  color: #67c23a;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
