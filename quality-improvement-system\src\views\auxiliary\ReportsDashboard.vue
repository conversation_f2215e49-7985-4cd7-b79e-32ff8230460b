<template>
  <div class="reports-dashboard">
    <!-- 关键指标看板 -->
    <el-row :gutter="20" class="kpi-row">
      <el-col :span="6">
        <el-card class="kpi-card">
          <div class="kpi-content">
            <div class="kpi-icon primary">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="kpi-info">
              <div class="kpi-number">{{ kpiData.totalProblems }}</div>
              <div class="kpi-label">总问题数</div>
              <div class="kpi-trend up">
                <el-icon><ArrowUp /></el-icon>
                +12%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="kpi-card">
          <div class="kpi-content">
            <div class="kpi-icon success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="kpi-info">
              <div class="kpi-number">{{ kpiData.resolvedRate }}%</div>
              <div class="kpi-label">问题解决率</div>
              <div class="kpi-trend up">
                <el-icon><ArrowUp /></el-icon>
                +5.2%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="kpi-card">
          <div class="kpi-content">
            <div class="kpi-icon warning">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="kpi-info">
              <div class="kpi-number">{{ kpiData.avgResolveTime }}</div>
              <div class="kpi-label">平均解决时间(天)</div>
              <div class="kpi-trend down">
                <el-icon><ArrowDown /></el-icon>
                -1.5天
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="kpi-card">
          <div class="kpi-content">
            <div class="kpi-icon danger">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="kpi-info">
              <div class="kpi-number">{{ kpiData.repeatRate }}%</div>
              <div class="kpi-label">重复问题率</div>
              <div class="kpi-trend down">
                <el-icon><ArrowDown /></el-icon>
                -2.1%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>问题数量趋势</span>
              <el-select v-model="trendPeriod" size="small" style="width: 120px;">
                <el-option label="最近7天" value="7days" />
                <el-option label="最近30天" value="30days" />
                <el-option label="最近3个月" value="3months" />
              </el-select>
            </div>
          </template>
          <div ref="trendChart" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>问题类型分布</span>
          </template>
          <div ref="typeChart" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>各部门问题分布</span>
          </template>
          <div ref="departmentChart" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>根因分析统计</span>
          </template>
          <div ref="rootCauseChart" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 自定义报表 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>自定义报表</span>
          <div class="header-actions">
            <el-button type="primary" @click="showReportDialog = true">
              <el-icon><Plus /></el-icon>
              新建报表
            </el-button>
            <el-button @click="exportReports">
              <el-icon><Download /></el-icon>
              导出报表
            </el-button>
          </div>
        </div>
      </template>

      <!-- 报表筛选 -->
      <div class="report-filters">
        <el-form :model="reportForm" inline>
          <el-form-item label="报表类型">
            <el-select v-model="reportForm.type" placeholder="请选择报表类型">
              <el-option label="问题统计报表" value="problem_stats" />
              <el-option label="解决周期报表" value="resolve_cycle" />
              <el-option label="部门绩效报表" value="department_performance" />
              <el-option label="根因分析报表" value="root_cause_analysis" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="reportForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item label="部门">
            <el-select v-model="reportForm.department" placeholder="请选择部门" clearable>
              <el-option label="生产部" value="生产部" />
              <el-option label="品保部" value="品保部" />
              <el-option label="设备部" value="设备部" />
              <el-option label="采购部" value="采购部" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="generateReport">
              <el-icon><Search /></el-icon>
              生成报表
            </el-button>
            <el-button @click="resetReportForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 报表结果 -->
      <el-table :data="reportData" style="width: 100%" v-loading="reportLoading">
        <el-table-column prop="metric" label="指标" width="200" />
        <el-table-column prop="value" label="数值" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="trend" label="趋势" width="100">
          <template #default="scope">
            <span :class="getTrendClass(scope.row.trend)">
              <el-icon v-if="scope.row.trend > 0"><ArrowUp /></el-icon>
              <el-icon v-else-if="scope.row.trend < 0"><ArrowDown /></el-icon>
              <el-icon v-else><Minus /></el-icon>
              {{ Math.abs(scope.row.trend) }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="说明" min-width="200" />
      </el-table>
    </el-card>

    <!-- 新建报表对话框 -->
    <el-dialog v-model="showReportDialog" title="新建自定义报表" width="600px">
      <el-form :model="newReportForm" label-width="120px">
        <el-form-item label="报表名称">
          <el-input v-model="newReportForm.name" placeholder="请输入报表名称" />
        </el-form-item>
        <el-form-item label="报表类型">
          <el-select v-model="newReportForm.type" placeholder="请选择报表类型" style="width: 100%">
            <el-option label="问题统计报表" value="problem_stats" />
            <el-option label="解决周期报表" value="resolve_cycle" />
            <el-option label="部门绩效报表" value="department_performance" />
            <el-option label="根因分析报表" value="root_cause_analysis" />
          </el-select>
        </el-form-item>
        <el-form-item label="统计维度">
          <el-checkbox-group v-model="newReportForm.dimensions">
            <el-checkbox label="时间">时间</el-checkbox>
            <el-checkbox label="部门">部门</el-checkbox>
            <el-checkbox label="问题类型">问题类型</el-checkbox>
            <el-checkbox label="风险等级">风险等级</el-checkbox>
            <el-checkbox label="根因类型">根因类型</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="统计指标">
          <el-checkbox-group v-model="newReportForm.metrics">
            <el-checkbox label="问题数量">问题数量</el-checkbox>
            <el-checkbox label="解决率">解决率</el-checkbox>
            <el-checkbox label="平均解决时间">平均解决时间</el-checkbox>
            <el-checkbox label="重复率">重复率</el-checkbox>
            <el-checkbox label="超期率">超期率</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="自动生成">
          <el-radio-group v-model="newReportForm.autoGenerate">
            <el-radio label="daily">每日</el-radio>
            <el-radio label="weekly">每周</el-radio>
            <el-radio label="monthly">每月</el-radio>
            <el-radio label="manual">手动</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showReportDialog = false">取消</el-button>
          <el-button type="primary" @click="createReport">创建</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

// 响应式数据
const reportLoading = ref(false)
const showReportDialog = ref(false)
const trendPeriod = ref('30days')
const trendChart = ref()
const typeChart = ref()
const departmentChart = ref()
const rootCauseChart = ref()

// KPI数据
const kpiData = ref({
  totalProblems: 156,
  resolvedRate: 87.5,
  avgResolveTime: 4.2,
  repeatRate: 8.3
})

// 报表表单
const reportForm = reactive({
  type: '',
  dateRange: [],
  department: ''
})

// 新建报表表单
const newReportForm = reactive({
  name: '',
  type: '',
  dimensions: [],
  metrics: [],
  autoGenerate: 'manual'
})

// 报表数据
const reportData = ref([
  {
    metric: '总问题数',
    value: 156,
    unit: '个',
    trend: 12,
    description: '本月新增问题数量较上月增长12%'
  },
  {
    metric: '问题解决率',
    value: 87.5,
    unit: '%',
    trend: 5.2,
    description: '问题解决效率持续提升'
  },
  {
    metric: '平均解决时间',
    value: 4.2,
    unit: '天',
    trend: -15.3,
    description: '解决时间较上月缩短1.5天'
  },
  {
    metric: '重复问题率',
    value: 8.3,
    unit: '%',
    trend: -2.1,
    description: '重复问题控制效果良好'
  },
  {
    metric: '超期问题数',
    value: 12,
    unit: '个',
    trend: -25,
    description: '超期问题数量显著减少'
  }
])

// 方法
const getTrendClass = (trend: number) => {
  if (trend > 0) return 'trend-up'
  if (trend < 0) return 'trend-down'
  return 'trend-stable'
}

const generateReport = () => {
  reportLoading.value = true
  setTimeout(() => {
    reportLoading.value = false
    ElMessage.success('报表生成成功')
  }, 2000)
}

const resetReportForm = () => {
  Object.assign(reportForm, {
    type: '',
    dateRange: [],
    department: ''
  })
}

const exportReports = () => {
  ElMessage.success('报表导出功能演示')
}

const createReport = () => {
  ElMessage.success('自定义报表创建成功')
  showReportDialog.value = false
  Object.assign(newReportForm, {
    name: '',
    type: '',
    dimensions: [],
    metrics: [],
    autoGenerate: 'manual'
  })
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    // 趋势图
    if (trendChart.value) {
      const chart1 = echarts.init(trendChart.value)
      const option1 = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['新增问题', '已解决问题']
        },
        xAxis: {
          type: 'category',
          data: ['1月1日', '1月2日', '1月3日', '1月4日', '1月5日', '1月6日', '1月7日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '新增问题',
            type: 'line',
            data: [12, 8, 15, 10, 6, 9, 11]
          },
          {
            name: '已解决问题',
            type: 'line',
            data: [10, 9, 12, 8, 7, 8, 10]
          }
        ]
      }
      chart1.setOption(option1)
    }

    // 问题类型分布图
    if (typeChart.value) {
      const chart2 = echarts.init(typeChart.value)
      const option2 = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '问题类型',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 35, name: '设备故障' },
              { value: 28, name: '工艺问题' },
              { value: 22, name: '人员操作' },
              { value: 15, name: '材料缺陷' }
            ]
          }
        ]
      }
      chart2.setOption(option2)
    }

    // 部门分布图
    if (departmentChart.value) {
      const chart3 = echarts.init(departmentChart.value)
      const option3 = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: ['生产部', '品保部', '设备部', '采购部']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '问题数量',
            type: 'bar',
            data: [45, 32, 28, 15],
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }
      chart3.setOption(option3)
    }

    // 根因统计图
    if (rootCauseChart.value) {
      const chart4 = echarts.init(rootCauseChart.value)
      const option4 = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'value'
        },
        yAxis: {
          type: 'category',
          data: ['培训不足', '设备维护', '工艺偏差', '材料问题', '环境因素']
        },
        series: [
          {
            name: '问题数量',
            type: 'bar',
            data: [42, 33, 17, 8, 5],
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      chart4.setOption(option4)
    }
  })
}

onMounted(() => {
  initCharts()
})
</script>

<style scoped>
.reports-dashboard {
  padding: 20px;
}

.kpi-row {
  margin-bottom: 20px;
}

.kpi-card {
  cursor: pointer;
  transition: all 0.3s;
}

.kpi-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.kpi-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.kpi-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.kpi-icon.primary {
  background-color: #409eff;
}

.kpi-icon.success {
  background-color: #67c23a;
}

.kpi-icon.warning {
  background-color: #e6a23c;
}

.kpi-icon.danger {
  background-color: #f56c6c;
}

.kpi-info {
  flex: 1;
}

.kpi-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.kpi-label {
  font-size: 14px;
  color: #909399;
  margin: 5px 0;
}

.kpi-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.kpi-trend.up {
  color: #67c23a;
}

.kpi-trend.down {
  color: #f56c6c;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.report-filters {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.trend-up {
  color: #67c23a;
}

.trend-down {
  color: #f56c6c;
}

.trend-stable {
  color: #909399;
}
</style>
