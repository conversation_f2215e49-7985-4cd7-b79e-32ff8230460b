<template>
  <div class="problem-closure">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>问题关闭</span>
          <el-button type="primary" @click="showClosureDialog = true">
            <el-icon><Plus /></el-icon>
            申请关闭
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="问题ID">
            <el-input v-model="searchForm.problemId" placeholder="请输入问题ID" clearable />
          </el-form-item>
          <el-form-item label="关闭状态">
            <el-select v-model="searchForm.status" placeholder="请选择关闭状态" clearable>
              <el-option label="待关闭" value="待关闭" />
              <el-option label="审核中" value="审核中" />
              <el-option label="已关闭" value="已关闭" />
              <el-option label="驳回" value="驳回" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 关闭列表 -->
      <el-table :data="closureList" style="width: 100%" v-loading="loading">
        <el-table-column prop="problemId" label="问题ID" width="140" />
        <el-table-column prop="problemDesc" label="问题描述" min-width="200" />
        <el-table-column prop="targetAchieved" label="目标达成" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.targetAchieved ? 'success' : 'danger'">
              {{ scope.row.targetAchieved ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sopUpdated" label="SOP更新" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.sopUpdated ? 'success' : 'warning'">
              {{ scope.row.sopUpdated ? '已更新' : '未更新' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="applicant" label="申请人" width="100" />
        <el-table-column prop="applyTime" label="申请时间" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="viewClosure(scope.row)">查看</el-button>
              <el-button 
                size="small" 
                type="primary" 
                @click="editClosure(scope.row)"
                :disabled="scope.row.status === '已关闭'"
              >
                编辑
              </el-button>
              <el-button 
                size="small" 
                type="success" 
                @click="approveClosure(scope.row)"
                v-if="scope.row.status === '审核中'"
              >
                审核
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 问题关闭对话框 -->
    <el-dialog
      v-model="showClosureDialog"
      :title="isEdit ? '编辑关闭申请' : '申请问题关闭'"
      width="1000px"
      @close="resetForm"
    >
      <el-form :model="closureForm" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="问题ID" prop="problemId">
              <el-select v-model="closureForm.problemId" placeholder="请选择问题" style="width: 100%">
                <el-option
                  v-for="problem in problemOptions"
                  :key="problem.id"
                  :label="problem.id"
                  :value="problem.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-input v-model="closureForm.applicant" placeholder="请输入申请人姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 关闭标准确认 -->
        <div class="closure-standards">
          <h4>关闭标准确认</h4>
          <el-form-item label="目标值达成" prop="targetAchieved">
            <el-radio-group v-model="closureForm.targetAchieved">
              <el-radio :label="true">是，已达成预设目标值</el-radio>
              <el-radio :label="false">否，未完全达成</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="目标达成说明" v-if="closureForm.targetAchieved">
            <el-input
              v-model="closureForm.targetDescription"
              type="textarea"
              :rows="3"
              placeholder="请详细说明目标达成情况和验证数据"
            />
          </el-form-item>

          <el-form-item label="未达成原因" v-if="!closureForm.targetAchieved">
            <el-input
              v-model="closureForm.failureReason"
              type="textarea"
              :rows="3"
              placeholder="请说明未达成的具体原因"
            />
          </el-form-item>

          <el-form-item label="SOP更新状态" prop="sopUpdated">
            <el-radio-group v-model="closureForm.sopUpdated">
              <el-radio :label="true">已更新相关SOP文件</el-radio>
              <el-radio :label="false">无需更新SOP</el-radio>
            </el-radio-group>
          </el-form-item>

          <div v-if="closureForm.sopUpdated" class="sop-section">
            <el-form-item label="更新的SOP">
              <el-input
                v-model="closureForm.sopDetails"
                type="textarea"
                :rows="3"
                placeholder="请列出已更新的SOP文件名称和修订内容"
              />
            </el-form-item>

            <el-form-item label="SOP签核状态">
              <el-checkbox-group v-model="closureForm.sopApprovals">
                <el-checkbox label="部门主管已签核">部门主管已签核</el-checkbox>
                <el-checkbox label="品保部已签核">品保部已签核</el-checkbox>
                <el-checkbox label="技术部已签核">技术部已签核</el-checkbox>
                <el-checkbox label="生产部已签核">生产部已签核</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="SOP文件上传">
              <el-upload
                class="upload-demo"
                drag
                action="#"
                multiple
                :auto-upload="false"
                :file-list="sopFileList"
                :on-change="handleSopFileChange"
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将SOP文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持上传PDF、Word等格式的SOP文件
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </div>
        </div>

        <!-- 经验沉淀 -->
        <div class="experience-section">
          <h4>经验沉淀</h4>
          <el-form-item label="解决经验" prop="experience">
            <el-input
              v-model="closureForm.experience"
              type="textarea"
              :rows="4"
              placeholder="请总结本次问题解决的成功经验和有效方法"
            />
          </el-form-item>

          <el-form-item label="教训总结" prop="lessons">
            <el-input
              v-model="closureForm.lessons"
              type="textarea"
              :rows="4"
              placeholder="请总结本次问题解决过程中的教训和需要改进的地方"
            />
          </el-form-item>

          <el-form-item label="预防措施">
            <el-input
              v-model="closureForm.preventiveMeasures"
              type="textarea"
              :rows="3"
              placeholder="为防止类似问题再次发生，建议采取的预防措施"
            />
          </el-form-item>

          <el-form-item label="知识分类">
            <el-select v-model="closureForm.knowledgeCategory" multiple placeholder="选择知识分类标签">
              <el-option label="设备维护" value="设备维护" />
              <el-option label="工艺改进" value="工艺改进" />
              <el-option label="人员培训" value="人员培训" />
              <el-option label="质量控制" value="质量控制" />
              <el-option label="供应商管理" value="供应商管理" />
              <el-option label="环境控制" value="环境控制" />
            </el-select>
          </el-form-item>
        </div>

        <!-- 关闭确认 -->
        <el-form-item label="关闭确认">
          <el-checkbox v-model="closureForm.confirmed">
            我确认以上信息准确无误，申请关闭此问题
          </el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showClosureDialog = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :disabled="!closureForm.confirmed">
            提交申请
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="showApprovalDialog"
      title="关闭审核"
      width="600px"
    >
      <el-form :model="approvalForm" label-width="120px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="approvalForm.result">
            <el-radio label="approved">通过</el-radio>
            <el-radio label="rejected">驳回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input
            v-model="approvalForm.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showApprovalDialog = false">取消</el-button>
          <el-button type="primary" @click="submitApproval">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showClosureDialog = ref(false)
const showApprovalDialog = ref(false)
const isEdit = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref<FormInstance>()
const sopFileList = ref<UploadFile[]>([])

// 搜索表单
const searchForm = reactive({
  problemId: '',
  status: ''
})

// 关闭表单
const closureForm = reactive({
  problemId: '',
  applicant: '',
  targetAchieved: true,
  targetDescription: '',
  failureReason: '',
  sopUpdated: false,
  sopDetails: '',
  sopApprovals: [],
  experience: '',
  lessons: '',
  preventiveMeasures: '',
  knowledgeCategory: [],
  confirmed: false
})

// 审核表单
const approvalForm = reactive({
  result: 'approved',
  comment: ''
})

// 表单验证规则
const rules: FormRules = {
  problemId: [
    { required: true, message: '请选择问题', trigger: 'change' }
  ],
  applicant: [
    { required: true, message: '请输入申请人', trigger: 'blur' }
  ],
  targetAchieved: [
    { required: true, message: '请确认目标达成情况', trigger: 'change' }
  ],
  sopUpdated: [
    { required: true, message: '请确认SOP更新状态', trigger: 'change' }
  ],
  experience: [
    { required: true, message: '请输入解决经验', trigger: 'blur' }
  ],
  lessons: [
    { required: true, message: '请输入教训总结', trigger: 'blur' }
  ]
}

// 问题选项
const problemOptions = ref([
  { id: 'LOP20250101001', description: '螺母孔径偏小' },
  { id: 'LOP20250101002', description: '焊接点虚焊' },
  { id: 'LOP20250101004', description: '设备温度异常' }
])

// 关闭列表数据
const closureList = ref([
  {
    problemId: 'LOP20250101001',
    problemDesc: '螺母孔径偏小（实测6.93mm, 标准7±0.05mm,偏小0.02mm）',
    targetAchieved: true,
    sopUpdated: true,
    applicant: '张工程师',
    applyTime: '2025-01-03 16:30:00',
    status: '已关闭'
  },
  {
    problemId: 'LOP20250101002',
    problemDesc: '焊接点虚焊导致接触不良',
    targetAchieved: false,
    sopUpdated: true,
    applicant: '李工程师',
    applyTime: '2025-01-05 14:15:00',
    status: '驳回'
  },
  {
    problemId: 'LOP20250101004',
    problemDesc: '设备温度异常波动',
    targetAchieved: true,
    sopUpdated: true,
    applicant: '王工程师',
    applyTime: '2025-01-07 18:20:00',
    status: '已关闭'
  },
  {
    problemId: 'LOP20250101006',
    problemDesc: '传送带表面划痕',
    targetAchieved: true,
    sopUpdated: false,
    applicant: '赵工程师',
    applyTime: '2025-01-04 13:45:00',
    status: '审核中'
  },
  {
    problemId: 'LOP20250101007',
    problemDesc: '包装密封不良',
    targetAchieved: true,
    sopUpdated: true,
    applicant: '钱工程师',
    applyTime: '2025-01-06 17:30:00',
    status: '待关闭'
  }
])

// 方法
const getStatusType = (status: string) => {
  switch (status) {
    case '待关闭': return 'info'
    case '审核中': return 'warning'
    case '已关闭': return 'success'
    case '驳回': return 'danger'
    default: return ''
  }
}

const handleSearch = () => {
  ElMessage.success('搜索功能演示')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    problemId: '',
    status: ''
  })
}

const viewClosure = (row: any) => {
  ElMessage.info(`查看关闭申请: ${row.problemId}`)
}

const editClosure = (row: any) => {
  isEdit.value = true
  Object.assign(closureForm, row)
  showClosureDialog.value = true
}

const approveClosure = (row: any) => {
  showApprovalDialog.value = true
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const handleSopFileChange = (file: UploadFile, fileList: UploadFile[]) => {
  // SOP文件上传处理
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  isEdit.value = false
  sopFileList.value = []
  Object.assign(closureForm, {
    problemId: '',
    applicant: '',
    targetAchieved: true,
    targetDescription: '',
    failureReason: '',
    sopUpdated: false,
    sopDetails: '',
    sopApprovals: [],
    experience: '',
    lessons: '',
    preventiveMeasures: '',
    knowledgeCategory: [],
    confirmed: false
  })
}

const submitForm = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        ElMessage.success('编辑成功')
      } else {
        ElMessage.success('关闭申请提交成功')
      }
      showClosureDialog.value = false
      resetForm()
    }
  })
}

const submitApproval = () => {
  if (approvalForm.result === 'approved') {
    ElMessage.success('审核通过，问题已关闭')
  } else {
    ElMessage.warning('审核驳回，已通知申请人修改')
  }
  showApprovalDialog.value = false
  Object.assign(approvalForm, {
    result: 'approved',
    comment: ''
  })
}

onMounted(() => {
  total.value = closureList.value.length
})
</script>

<style scoped>
.problem-closure {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.closure-standards,
.experience-section {
  margin: 20px 0;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.closure-standards h4,
.experience-section h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.sop-section {
  margin-top: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.upload-demo {
  width: 100%;
}
</style>
