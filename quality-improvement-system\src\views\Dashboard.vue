<template>
  <div class="dashboard">
    <h1>工厂持续改善管理系统</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon red">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.highRisk }}</div>
              <div class="stat-label">高危问题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon orange">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.pending }}</div>
              <div class="stat-label">待处理问题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon blue">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.inProgress }}</div>
              <div class="stat-label">处理中问题</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon green">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.completed }}</div>
              <div class="stat-label">已完成问题</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions" style="margin-top: 20px;">
      <template #header>
        <span>快速操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" size="large" @click="$router.push('/problem-register')" style="width: 100%;">
            <el-icon><EditPen /></el-icon>
            问题登记
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" size="large" @click="$router.push('/root-cause-analysis')" style="width: 100%;">
            <el-icon><Search /></el-icon>
            根因分析
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" size="large" @click="$router.push('/action-plan')" style="width: 100%;">
            <el-icon><List /></el-icon>
            行动方案
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" size="large" @click="$router.push('/risk-warning')" style="width: 100%;">
            <el-icon><Monitor /></el-icon>
            风险预警
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 最近问题 -->
    <el-card class="recent-problems" style="margin-top: 20px;">
      <template #header>
        <span>最近问题</span>
      </template>
      <el-table :data="recentProblems" style="width: 100%">
        <el-table-column prop="id" label="问题ID" width="120" />
        <el-table-column prop="description" label="问题描述" />
        <el-table-column prop="riskLevel" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskLevelType(scope.row.riskLevel)">
              {{ scope.row.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const stats = ref({
  highRisk: 5,
  pending: 12,
  inProgress: 8,
  completed: 45
})

const recentProblems = ref([
  {
    id: 'LOP20250101001',
    description: '螺母孔径偏小（实测6.93mm, 标准7±0.05mm,偏小0.02mm）',
    riskLevel: '高危',
    status: '待分析',
    createTime: '2025-01-01 09:30:00'
  },
  {
    id: 'LOP20250101002',
    description: '焊接点虚焊导致接触不良',
    riskLevel: '中危',
    status: '执行中',
    createTime: '2025-01-01 10:15:00'
  },
  {
    id: 'LOP20250101003',
    description: '包装标签贴歪',
    riskLevel: '低危',
    status: '待关闭',
    createTime: '2025-01-01 11:00:00'
  },
  {
    id: 'LOP20250101004',
    description: '设备温度异常波动',
    riskLevel: '高危',
    status: '待制定方案',
    createTime: '2025-01-01 14:20:00'
  },
  {
    id: 'LOP20250101005',
    description: '操作员未按SOP执行',
    riskLevel: '中危',
    status: '已关闭',
    createTime: '2025-01-01 15:45:00'
  }
])

const getRiskLevelType = (level: string) => {
  switch (level) {
    case '高危': return 'danger'
    case '中危': return 'warning'
    case '低危': return 'info'
    default: return ''
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case '待分析': return 'warning'
    case '执行中': return 'primary'
    case '待关闭': return 'success'
    case '已关闭': return 'info'
    default: return ''
  }
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stats-cards {
  margin-top: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.red {
  background-color: #f56c6c;
}

.stat-icon.orange {
  background-color: #e6a23c;
}

.stat-icon.blue {
  background-color: #409eff;
}

.stat-icon.green {
  background-color: #67c23a;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.quick-actions .el-button {
  height: 60px;
  font-size: 16px;
}
</style>
