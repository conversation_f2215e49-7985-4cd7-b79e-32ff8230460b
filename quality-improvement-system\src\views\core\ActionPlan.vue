<template>
  <div class="action-plan">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>行动方案</span>
          <el-button type="primary" @click="showPlanDialog = true">
            <el-icon><Plus /></el-icon>
            新增方案
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="问题ID">
            <el-input v-model="searchForm.problemId" placeholder="请输入问题ID" clearable />
          </el-form-item>
          <el-form-item label="方案状态">
            <el-select v-model="searchForm.status" placeholder="请选择方案状态" clearable>
              <el-option label="待制定" value="待制定" />
              <el-option label="待审核" value="待审核" />
              <el-option label="执行中" value="执行中" />
              <el-option label="已完成" value="已完成" />
            </el-select>
          </el-form-item>
          <el-form-item label="责任人">
            <el-input v-model="searchForm.responsible" placeholder="请输入责任人" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 方案列表 -->
      <el-table :data="planList" style="width: 100%" v-loading="loading">
        <el-table-column prop="problemId" label="问题ID" width="140" />
        <el-table-column prop="planTitle" label="方案标题" min-width="180" />
        <el-table-column prop="responsible" label="责任人" width="100" />
        <el-table-column prop="deadline" label="完成期限" width="120" />
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="scope">
            <el-progress :percentage="scope.row.progress" :status="getProgressStatus(scope.row.progress)" />
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="viewPlan(scope.row)">查看</el-button>
              <el-button size="small" type="primary" @click="editPlan(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deletePlan(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 行动方案对话框 -->
    <el-dialog
      v-model="showPlanDialog"
      :title="isEdit ? '编辑行动方案' : '新增行动方案'"
      width="1000px"
      @close="resetForm"
    >
      <el-form :model="planForm" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="问题ID" prop="problemId">
              <el-select v-model="planForm.problemId" placeholder="请选择问题" style="width: 100%">
                <el-option
                  v-for="problem in problemOptions"
                  :key="problem.id"
                  :label="problem.id"
                  :value="problem.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方案标题" prop="planTitle">
              <el-input v-model="planForm.planTitle" placeholder="请输入方案标题" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 5W1H分析 -->
        <div class="analysis-section">
          <h4>5W1H分析</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="What (做什么)" prop="what">
                <el-input
                  v-model="planForm.what"
                  type="textarea"
                  :rows="3"
                  placeholder="具体改善措施"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Why (为什么做)" prop="why">
                <el-input
                  v-model="planForm.why"
                  type="textarea"
                  :rows="3"
                  placeholder="措施的目的和预期效果"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="When (何时完成)" prop="when">
                <el-date-picker
                  v-model="planForm.when"
                  type="datetime"
                  placeholder="选择完成时限"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Who (谁负责)" prop="who">
                <el-select v-model="planForm.who" placeholder="选择责任人" style="width: 100%">
                  <el-option label="张工程师" value="张工程师" />
                  <el-option label="李工程师" value="李工程师" />
                  <el-option label="王工程师" value="王工程师" />
                  <el-option label="赵工程师" value="赵工程师" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="Where (在哪里)" prop="where">
                <el-input
                  v-model="planForm.where"
                  placeholder="措施实施的地点或范围"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="How (如何做)" prop="how">
                <el-input
                  v-model="planForm.how"
                  type="textarea"
                  :rows="3"
                  placeholder="措施的具体步骤和方法"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 任务分配 -->
        <div class="task-section">
          <h4>任务分配</h4>
          <el-button type="primary" size="small" @click="addTask" style="margin-bottom: 15px;">
            <el-icon><Plus /></el-icon>
            添加子任务
          </el-button>
          <div v-for="(task, index) in tasks" :key="index" class="task-item">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="`任务${index + 1}`">
                  <el-input v-model="task.name" placeholder="任务名称" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="责任人">
                  <el-select v-model="task.responsible" placeholder="选择责任人">
                    <el-option label="张工程师" value="张工程师" />
                    <el-option label="李工程师" value="李工程师" />
                    <el-option label="王工程师" value="王工程师" />
                    <el-option label="赵工程师" value="赵工程师" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="完成期限">
                  <el-date-picker
                    v-model="task.deadline"
                    type="date"
                    placeholder="选择日期"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item label="操作">
                  <el-button type="danger" size="small" @click="removeTask(index)">删除</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="任务描述">
              <el-input v-model="task.description" type="textarea" :rows="2" placeholder="详细描述任务内容" />
            </el-form-item>
          </div>
        </div>

        <!-- 跨部门协作 -->
        <el-form-item label="需要协作部门">
          <el-checkbox-group v-model="planForm.collaborationDepts">
            <el-checkbox label="生产部">生产部</el-checkbox>
            <el-checkbox label="品保部">品保部</el-checkbox>
            <el-checkbox label="设备部">设备部</el-checkbox>
            <el-checkbox label="采购部">采购部</el-checkbox>
            <el-checkbox label="PIE部">PIE部</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="附件上传">
          <el-upload
            class="upload-demo"
            drag
            action="#"
            multiple
            :auto-upload="false"
            :file-list="fileList"
            :on-change="handleFileChange"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持上传操作指导书、设备校准记录等相关文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPlanDialog = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules, UploadFile } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showPlanDialog = ref(false)
const isEdit = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref<FormInstance>()
const fileList = ref<UploadFile[]>([])

// 搜索表单
const searchForm = reactive({
  problemId: '',
  status: '',
  responsible: ''
})

// 方案表单
const planForm = reactive({
  problemId: '',
  planTitle: '',
  what: '',
  why: '',
  when: '',
  who: '',
  where: '',
  how: '',
  collaborationDepts: []
})

// 任务列表
const tasks = ref([
  { name: '', responsible: '', deadline: '', description: '' }
])

// 表单验证规则
const rules: FormRules = {
  problemId: [
    { required: true, message: '请选择问题', trigger: 'change' }
  ],
  planTitle: [
    { required: true, message: '请输入方案标题', trigger: 'blur' }
  ],
  what: [
    { required: true, message: '请输入具体改善措施', trigger: 'blur' }
  ],
  why: [
    { required: true, message: '请输入措施目的', trigger: 'blur' }
  ],
  when: [
    { required: true, message: '请选择完成时限', trigger: 'change' }
  ],
  who: [
    { required: true, message: '请选择责任人', trigger: 'change' }
  ],
  where: [
    { required: true, message: '请输入实施地点', trigger: 'blur' }
  ],
  how: [
    { required: true, message: '请输入具体步骤', trigger: 'blur' }
  ]
}

// 问题选项
const problemOptions = ref([
  { id: 'LOP20250101001', description: '螺母孔径偏小' },
  { id: 'LOP20250101002', description: '焊接点虚焊' },
  { id: 'LOP20250101004', description: '设备温度异常' }
])

// 方案列表数据
const planList = ref([
  {
    problemId: 'LOP20250101001',
    planTitle: '设备校准与点检优化方案',
    responsible: '张工程师',
    deadline: '2025-01-03',
    progress: 75,
    status: '执行中',
    createTime: '2025-01-01 11:30:00'
  },
  {
    problemId: 'LOP20250101002',
    planTitle: '焊接工艺改进方案',
    responsible: '李工程师',
    deadline: '2025-01-05',
    progress: 30,
    status: '执行中',
    createTime: '2025-01-01 12:15:00'
  },
  {
    problemId: 'LOP20250101004',
    planTitle: '温控系统升级方案',
    responsible: '王工程师',
    deadline: '2025-01-07',
    progress: 100,
    status: '已完成',
    createTime: '2025-01-01 16:20:00'
  },
  {
    problemId: 'LOP20250101006',
    planTitle: '传送带维护改进方案',
    responsible: '赵工程师',
    deadline: '2025-01-04',
    progress: 0,
    status: '待制定',
    createTime: '2025-01-01 17:45:00'
  },
  {
    problemId: 'LOP20250101007',
    planTitle: '包装密封标准化方案',
    responsible: '钱工程师',
    deadline: '2025-01-06',
    progress: 60,
    status: '执行中',
    createTime: '2025-01-01 18:30:00'
  }
])

// 方法
const getStatusType = (status: string) => {
  switch (status) {
    case '待制定': return 'info'
    case '待审核': return 'warning'
    case '执行中': return 'primary'
    case '已完成': return 'success'
    default: return ''
  }
}

const getProgressStatus = (progress: number) => {
  if (progress === 100) return 'success'
  if (progress >= 80) return ''
  if (progress >= 50) return 'warning'
  return 'exception'
}

const handleSearch = () => {
  ElMessage.success('搜索功能演示')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    problemId: '',
    status: '',
    responsible: ''
  })
}

const viewPlan = (row: any) => {
  ElMessage.info(`查看方案: ${row.problemId}`)
}

const editPlan = (row: any) => {
  isEdit.value = true
  Object.assign(planForm, row)
  showPlanDialog.value = true
}

const deletePlan = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除方案 ${row.problemId} 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const addTask = () => {
  tasks.value.push({ name: '', responsible: '', deadline: '', description: '' })
}

const removeTask = (index: number) => {
  if (tasks.value.length > 1) {
    tasks.value.splice(index, 1)
  }
}

const handleFileChange = (file: UploadFile, fileList: UploadFile[]) => {
  // 文件上传处理
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  isEdit.value = false
  fileList.value = []
  tasks.value = [{ name: '', responsible: '', deadline: '', description: '' }]
  Object.assign(planForm, {
    problemId: '',
    planTitle: '',
    what: '',
    why: '',
    when: '',
    who: '',
    where: '',
    how: '',
    collaborationDepts: []
  })
}

const submitForm = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        ElMessage.success('编辑成功')
      } else {
        ElMessage.success('新增成功')
      }
      showPlanDialog.value = false
      resetForm()
    }
  })
}

onMounted(() => {
  total.value = planList.value.length
})
</script>

<style scoped>
.action-plan {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.analysis-section,
.task-section {
  margin: 20px 0;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.analysis-section h4,
.task-section h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.task-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 4px;
  border-left: 4px solid #67c23a;
}

.upload-demo {
  width: 100%;
}
</style>
