<template>
  <div class="effect-verification">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>效果验证</span>
          <el-button type="primary" @click="showVerificationDialog = true">
            <el-icon><Plus /></el-icon>
            新增验证
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="问题ID">
            <el-input v-model="searchForm.problemId" placeholder="请输入问题ID" clearable />
          </el-form-item>
          <el-form-item label="验证状态">
            <el-select v-model="searchForm.status" placeholder="请选择验证状态" clearable>
              <el-option label="待验证" value="待验证" />
              <el-option label="验证中" value="验证中" />
              <el-option label="已达成" value="已达成" />
              <el-option label="部分达成" value="部分达成" />
              <el-option label="未达成" value="未达成" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 验证列表 -->
      <el-table :data="verificationList" style="width: 100%" v-loading="loading">
        <el-table-column prop="problemId" label="问题ID" width="140" />
        <el-table-column prop="planTitle" label="方案标题" min-width="180" />
        <el-table-column prop="targetValue" label="目标值" width="120" />
        <el-table-column prop="actualValue" label="实际值" width="120" />
        <el-table-column prop="improvementRate" label="改善率" width="100">
          <template #default="scope">
            <span :class="getImprovementClass(scope.row.improvementRate)">
              {{ scope.row.improvementRate }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="verifier" label="验证人" width="100" />
        <el-table-column prop="verifyTime" label="验证时间" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="viewVerification(scope.row)">查看</el-button>
              <el-button size="small" type="primary" @click="editVerification(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteVerification(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 效果验证对话框 -->
    <el-dialog
      v-model="showVerificationDialog"
      :title="isEdit ? '编辑效果验证' : '新增效果验证'"
      width="1200px"
      @close="resetForm"
    >
      <el-form :model="verificationForm" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="问题ID" prop="problemId">
              <el-select v-model="verificationForm.problemId" placeholder="请选择问题" style="width: 100%">
                <el-option
                  v-for="problem in problemOptions"
                  :key="problem.id"
                  :label="problem.id"
                  :value="problem.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方案标题" prop="planTitle">
              <el-input v-model="verificationForm.planTitle" placeholder="请输入方案标题" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 数据收集与对比 -->
        <div class="data-section">
          <h4>数据收集与对比</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="改善前数据" prop="beforeData">
                <el-input-number
                  v-model="verificationForm.beforeData"
                  :precision="2"
                  placeholder="改善前数据"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="改善后数据" prop="afterData">
                <el-input-number
                  v-model="verificationForm.afterData"
                  :precision="2"
                  placeholder="改善后数据"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="目标值" prop="targetValue">
                <el-input-number
                  v-model="verificationForm.targetValue"
                  :precision="2"
                  placeholder="目标值"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="数据单位">
                <el-input v-model="verificationForm.dataUnit" placeholder="如：mm、%、ppm等" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="改善率">
                <el-input
                  v-model="improvementRate"
                  readonly
                  placeholder="自动计算"
                  style="width: 100%"
                >
                  <template #suffix>%</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- SPC工具选择 -->
        <div class="spc-section">
          <h4>SPC工具分析</h4>
          <el-form-item label="分析工具">
            <el-checkbox-group v-model="verificationForm.spcTools">
              <el-checkbox label="控制图">控制图 (Control Chart)</el-checkbox>
              <el-checkbox label="柏拉图">柏拉图 (Pareto Chart)</el-checkbox>
              <el-checkbox label="趋势图">趋势图 (Trend Chart)</el-checkbox>
              <el-checkbox label="直方图">直方图 (Histogram)</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <!-- 图表展示区域 -->
          <div v-if="verificationForm.spcTools.length > 0" class="chart-area">
            <el-tabs v-model="activeChart">
              <el-tab-pane
                v-for="tool in verificationForm.spcTools"
                :key="tool"
                :label="tool"
                :name="tool"
              >
                <div class="chart-container">
                  <div v-if="tool === '控制图'" ref="controlChart" style="width: 100%; height: 300px;"></div>
                  <div v-if="tool === '柏拉图'" ref="paretoChart" style="width: 100%; height: 300px;"></div>
                  <div v-if="tool === '趋势图'" ref="trendChart" style="width: 100%; height: 300px;"></div>
                  <div v-if="tool === '直方图'" ref="histogramChart" style="width: 100%; height: 300px;"></div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="验证结论" prop="conclusion">
              <el-select v-model="verificationForm.conclusion" placeholder="请选择验证结论" style="width: 100%">
                <el-option label="已达成目标值" value="已达成目标值" />
                <el-option label="部分达成目标值" value="部分达成目标值" />
                <el-option label="未达成目标值" value="未达成目标值" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="验证人员" prop="verifier">
              <el-input v-model="verificationForm.verifier" placeholder="请输入验证人员姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="验证说明">
          <el-input
            v-model="verificationForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细说明验证过程和结果"
          />
        </el-form-item>

        <el-form-item label="后续建议">
          <el-input
            v-model="verificationForm.suggestion"
            type="textarea"
            :rows="3"
            placeholder="如果未达成目标，请提出后续改进建议"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showVerificationDialog = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const showVerificationDialog = ref(false)
const isEdit = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref<FormInstance>()
const activeChart = ref('')

// 搜索表单
const searchForm = reactive({
  problemId: '',
  status: ''
})

// 验证表单
const verificationForm = reactive({
  problemId: '',
  planTitle: '',
  beforeData: 0,
  afterData: 0,
  targetValue: 0,
  dataUnit: '',
  spcTools: [],
  conclusion: '',
  verifier: '',
  description: '',
  suggestion: ''
})

// 计算改善率
const improvementRate = computed(() => {
  if (verificationForm.beforeData && verificationForm.afterData) {
    const rate = ((verificationForm.beforeData - verificationForm.afterData) / verificationForm.beforeData * 100)
    return rate.toFixed(2)
  }
  return '0.00'
})

// 表单验证规则
const rules: FormRules = {
  problemId: [
    { required: true, message: '请选择问题', trigger: 'change' }
  ],
  planTitle: [
    { required: true, message: '请输入方案标题', trigger: 'blur' }
  ],
  beforeData: [
    { required: true, message: '请输入改善前数据', trigger: 'blur' }
  ],
  afterData: [
    { required: true, message: '请输入改善后数据', trigger: 'blur' }
  ],
  targetValue: [
    { required: true, message: '请输入目标值', trigger: 'blur' }
  ],
  conclusion: [
    { required: true, message: '请选择验证结论', trigger: 'change' }
  ],
  verifier: [
    { required: true, message: '请输入验证人员', trigger: 'blur' }
  ]
}

// 问题选项
const problemOptions = ref([
  { id: 'LOP20250101001', description: '螺母孔径偏小' },
  { id: 'LOP20250101002', description: '焊接点虚焊' },
  { id: 'LOP20250101004', description: '设备温度异常' }
])

// 验证列表数据
const verificationList = ref([
  {
    problemId: 'LOP20250101001',
    planTitle: '设备校准与点检优化方案',
    targetValue: '7.00±0.05',
    actualValue: '6.98±0.02',
    improvementRate: 85.5,
    verifier: '品保张工',
    verifyTime: '2025-01-03 14:30:00',
    status: '已达成'
  },
  {
    problemId: 'LOP20250101002',
    planTitle: '焊接工艺改进方案',
    targetValue: '<0.1%',
    actualValue: '0.15%',
    improvementRate: 65.2,
    verifier: '品保李工',
    verifyTime: '2025-01-05 10:15:00',
    status: '部分达成'
  },
  {
    problemId: 'LOP20250101004',
    planTitle: '温控系统升级方案',
    targetValue: '±1°C',
    actualValue: '±0.5°C',
    improvementRate: 92.3,
    verifier: '品保王工',
    verifyTime: '2025-01-07 16:20:00',
    status: '已达成'
  },
  {
    problemId: 'LOP20250101006',
    planTitle: '传送带维护改进方案',
    targetValue: '0次/天',
    actualValue: '2次/天',
    improvementRate: 45.8,
    verifier: '品保赵工',
    verifyTime: '2025-01-04 11:45:00',
    status: '未达成'
  },
  {
    problemId: 'LOP20250101007',
    planTitle: '包装密封标准化方案',
    targetValue: '<0.5%',
    actualValue: '0.3%',
    improvementRate: 78.9,
    verifier: '品保钱工',
    verifyTime: '2025-01-06 15:30:00',
    status: '已达成'
  }
])

// 方法
const getStatusType = (status: string) => {
  switch (status) {
    case '待验证': return 'info'
    case '验证中': return 'warning'
    case '已达成': return 'success'
    case '部分达成': return 'warning'
    case '未达成': return 'danger'
    default: return ''
  }
}

const getImprovementClass = (rate: number) => {
  if (rate >= 80) return 'improvement-excellent'
  if (rate >= 60) return 'improvement-good'
  if (rate >= 40) return 'improvement-fair'
  return 'improvement-poor'
}

const handleSearch = () => {
  ElMessage.success('搜索功能演示')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    problemId: '',
    status: ''
  })
}

const viewVerification = (row: any) => {
  ElMessage.info(`查看验证: ${row.problemId}`)
}

const editVerification = (row: any) => {
  isEdit.value = true
  Object.assign(verificationForm, row)
  showVerificationDialog.value = true
}

const deleteVerification = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除验证 ${row.problemId} 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const initCharts = () => {
  nextTick(() => {
    // 这里可以初始化各种图表
    // 由于是演示，暂时显示占位符
  })
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  isEdit.value = false
  Object.assign(verificationForm, {
    problemId: '',
    planTitle: '',
    beforeData: 0,
    afterData: 0,
    targetValue: 0,
    dataUnit: '',
    spcTools: [],
    conclusion: '',
    verifier: '',
    description: '',
    suggestion: ''
  })
}

const submitForm = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        ElMessage.success('编辑成功')
      } else {
        ElMessage.success('新增成功')
      }
      showVerificationDialog.value = false
      resetForm()
    }
  })
}

onMounted(() => {
  total.value = verificationList.value.length
})
</script>

<style scoped>
.effect-verification {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.data-section,
.spc-section {
  margin: 20px 0;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.data-section h4,
.spc-section h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.chart-area {
  margin-top: 20px;
}

.chart-container {
  background-color: white;
  border-radius: 4px;
  padding: 20px;
}

.improvement-excellent {
  color: #67c23a;
  font-weight: bold;
}

.improvement-good {
  color: #409eff;
  font-weight: bold;
}

.improvement-fair {
  color: #e6a23c;
  font-weight: bold;
}

.improvement-poor {
  color: #f56c6c;
  font-weight: bold;
}
</style>
