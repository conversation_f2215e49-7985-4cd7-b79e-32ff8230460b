<template>
  <el-container class="admin-layout">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
      <div class="logo">
        <h3 v-if="!isCollapse">持续改善系统</h3>
        <h3 v-else>改善</h3>
      </div>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router
      >
        <!-- 核心业务模块 -->
        <el-sub-menu index="core">
          <template #title>
            <el-icon><Management /></el-icon>
            <span>核心业务</span>
          </template>
          <el-menu-item index="/problem-register">
            <el-icon><EditPen /></el-icon>
            <span>问题登记</span>
          </el-menu-item>
          <el-menu-item index="/root-cause-analysis">
            <el-icon><Search /></el-icon>
            <span>根因分析</span>
          </el-menu-item>
          <el-menu-item index="/action-plan">
            <el-icon><List /></el-icon>
            <span>行动方案</span>
          </el-menu-item>
          <el-menu-item index="/effect-verification">
            <el-icon><DataAnalysis /></el-icon>
            <span>效果验证</span>
          </el-menu-item>
          <el-menu-item index="/problem-closure">
            <el-icon><CircleCheck /></el-icon>
            <span>问题关闭</span>
          </el-menu-item>
        </el-sub-menu>

        <!-- 辅助管理模块 -->
        <el-sub-menu index="auxiliary">
          <template #title>
            <el-icon><Monitor /></el-icon>
            <span>辅助管理</span>
          </template>
          <el-menu-item index="/risk-warning">
            <el-icon><Warning /></el-icon>
            <span>风险预警</span>
          </el-menu-item>
          <el-menu-item index="/repeat-problem">
            <el-icon><Refresh /></el-icon>
            <span>重复问题管理</span>
          </el-menu-item>
          <el-menu-item index="/knowledge-base">
            <el-icon><Reading /></el-icon>
            <span>知识库管理</span>
          </el-menu-item>
          <el-menu-item index="/reports-dashboard">
            <el-icon><PieChart /></el-icon>
            <span>报表与看板</span>
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleCollapse"
            class="collapse-btn"
          >
            <el-icon><Fold v-if="!isCollapse" /><Expand v-else /></el-icon>
          </el-button>
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path" :to="{ path: item.path }">
              {{ item.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div class="header-right">
          <el-dropdown>
            <span class="user-info">
              <el-icon><User /></el-icon>
              管理员
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人中心</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主要内容 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isCollapse = ref(false)

const activeMenu = computed(() => route.path)

const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    name: item.meta?.title,
    path: item.path
  }))
})

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style scoped>
.admin-layout {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  transition: width 0.3s;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
}

.logo h3 {
  margin: 0;
  font-size: 16px;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.collapse-btn {
  font-size: 18px;
  color: #606266;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  color: #606266;
}

.main-content {
  background-color: #f5f5f5;
  padding: 20px;
}

.el-menu {
  border-right: none;
}
</style>
