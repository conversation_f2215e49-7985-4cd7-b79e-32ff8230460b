# 工厂持续改善管理系统

基于Vue 3 + Element Plus的工厂质量问题持续改善管理系统原型图。

## 系统概述

本系统旨在实现工厂质量问题的标准化管理、高效追踪、根因分析、措施执行与效果验证，并通过数字化手段提升协作效率和知识沉淀。

## 功能模块

### 核心业务模块
- **问题登记**：问题信息录入、风险分级、文件上传
- **根因分析**：5Why分析工具、鱼骨图分析、因果矩阵
- **行动方案**：5W1H分析、任务分配、跨部门协作
- **效果验证**：数据对比、SPC工具、验证结论
- **问题关闭**：关闭标准确认、经验沉淀、知识归档

### 辅助管理模块
- **风险预警**：三色预警看板、超期提醒、自动上报
- **重复问题管理**：TOP问题根因库、相似问题预警
- **知识库管理**：经典案例库、失败教训库、知识分类
- **报表与看板**：自定义报表、可视化看板、趋势分析

## 技术栈

- **前端框架**：Vue 3 + TypeScript
- **UI组件库**：Element Plus
- **图表库**：ECharts + Vue-ECharts
- **构建工具**：Vite
- **状态管理**：Pinia
- **路由管理**：Vue Router

## 项目启动

### 安装依赖
```sh
npm install
```

### 开发环境启动
```sh
npm run dev
```

### 生产环境构建
```sh
npm run build
```

### 代码检查
```sh
npm run lint
```

## 系统特色

1. **标准化流程**：遵循PDCA循环，确保问题解决的系统性
2. **可视化分析**：提供多种分析工具和图表展示
3. **知识沉淀**：自动归档成功案例和失败教训
4. **智能预警**：三色预警机制，及时发现风险
5. **协作管理**：支持跨部门协作和任务分配

## 演示数据

系统内置了丰富的演示数据，包括：
- 5条问题登记示例
- 5条根因分析案例
- 5条行动方案记录
- 5条效果验证数据
- 5条问题关闭记录
- 完整的知识库案例

## 注意事项

- 本系统为原型图演示，不包含后端API
- 所有数据均为前端模拟数据
- 建议使用Chrome浏览器访问以获得最佳体验

## 访问地址

开发环境：http://localhost:5173/
