<template>
  <div class="root-cause-analysis">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>根因分析</span>
          <el-button type="primary" @click="showAnalysisDialog = true">
            <el-icon><Plus /></el-icon>
            新增分析
          </el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="问题ID">
            <el-input v-model="searchForm.problemId" placeholder="请输入问题ID" clearable />
          </el-form-item>
          <el-form-item label="分析状态">
            <el-select v-model="searchForm.status" placeholder="请选择分析状态" clearable>
              <el-option label="待分析" value="待分析" />
              <el-option label="分析中" value="分析中" />
              <el-option label="已完成" value="已完成" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 分析列表 -->
      <el-table :data="analysisList" style="width: 100%" v-loading="loading">
        <el-table-column prop="problemId" label="问题ID" width="140" />
        <el-table-column prop="problemDesc" label="问题描述" min-width="200" />
        <el-table-column prop="analysisMethod" label="分析方法" width="120" />
        <el-table-column prop="rootCause" label="根本原因" min-width="180" />
        <el-table-column prop="analyst" label="分析人员" width="100" />
        <el-table-column prop="createTime" label="分析时间" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="viewAnalysis(scope.row)">查看</el-button>
              <el-button size="small" type="primary" @click="editAnalysis(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteAnalysis(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 根因分析对话框 -->
    <el-dialog
      v-model="showAnalysisDialog"
      :title="isEdit ? '编辑根因分析' : '新增根因分析'"
      width="1000px"
      @close="resetForm"
    >
      <el-form :model="analysisForm" :rules="rules" ref="formRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="问题ID" prop="problemId">
              <el-select v-model="analysisForm.problemId" placeholder="请选择问题" style="width: 100%">
                <el-option
                  v-for="problem in problemOptions"
                  :key="problem.id"
                  :label="problem.id"
                  :value="problem.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分析方法" prop="analysisMethod">
              <el-select v-model="analysisForm.analysisMethod" placeholder="请选择分析方法" style="width: 100%">
                <el-option label="5Why分析" value="5Why分析" />
                <el-option label="鱼骨图分析" value="鱼骨图分析" />
                <el-option label="因果矩阵" value="因果矩阵" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 5Why分析 -->
        <div v-if="analysisForm.analysisMethod === '5Why分析'" class="analysis-section">
          <h4>5Why分析</h4>
          <div v-for="(why, index) in whyAnalysis" :key="index" class="why-item">
            <el-form-item :label="`Why ${index + 1}`">
              <el-input
                v-model="why.question"
                placeholder="为什么会发生这个问题？"
                style="margin-bottom: 10px"
              />
              <el-input
                v-model="why.answer"
                placeholder="回答"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
            <el-button
              v-if="index === whyAnalysis.length - 1 && index < 4"
              type="text"
              @click="addWhy"
            >
              + 添加下一个Why
            </el-button>
          </div>
        </div>

        <!-- 鱼骨图分析 -->
        <div v-if="analysisForm.analysisMethod === '鱼骨图分析'" class="analysis-section">
          <h4>鱼骨图分析</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="人员因素">
                <el-input v-model="fishboneAnalysis.people" type="textarea" :rows="3" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="机器设备">
                <el-input v-model="fishboneAnalysis.machine" type="textarea" :rows="3" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="材料因素">
                <el-input v-model="fishboneAnalysis.material" type="textarea" :rows="3" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="方法工艺">
                <el-input v-model="fishboneAnalysis.method" type="textarea" :rows="3" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="环境因素">
                <el-input v-model="fishboneAnalysis.environment" type="textarea" :rows="3" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="测量检测">
                <el-input v-model="fishboneAnalysis.measurement" type="textarea" :rows="3" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <el-form-item label="根本原因" prop="rootCause">
          <el-input
            v-model="analysisForm.rootCause"
            type="textarea"
            :rows="4"
            placeholder="请描述最终确定的根本原因"
          />
        </el-form-item>

        <el-form-item label="根因类型" prop="rootCauseType">
          <el-select v-model="analysisForm.rootCauseType" placeholder="请选择根因类型" style="width: 100%">
            <el-option label="人员培训不足" value="人员培训不足" />
            <el-option label="设备故障" value="设备故障" />
            <el-option label="工艺流程缺陷" value="工艺流程缺陷" />
            <el-option label="材料质量问题" value="材料质量问题" />
            <el-option label="环境条件不当" value="环境条件不当" />
            <el-option label="检测方法不当" value="检测方法不当" />
          </el-select>
        </el-form-item>

        <el-form-item label="关联历史问题">
          <el-select
            v-model="analysisForm.relatedProblems"
            multiple
            placeholder="选择相似的历史问题"
            style="width: 100%"
          >
            <el-option
              v-for="problem in historyProblems"
              :key="problem.id"
              :label="`${problem.id} - ${problem.description}`"
              :value="problem.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="分析人员" prop="analyst">
          <el-input v-model="analysisForm.analyst" placeholder="请输入分析人员姓名" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAnalysisDialog = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// 响应式数据
const loading = ref(false)
const showAnalysisDialog = ref(false)
const isEdit = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const formRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  problemId: '',
  status: ''
})

// 分析表单
const analysisForm = reactive({
  problemId: '',
  analysisMethod: '',
  rootCause: '',
  rootCauseType: '',
  relatedProblems: [],
  analyst: ''
})

// 5Why分析数据
const whyAnalysis = ref([
  { question: '', answer: '' }
])

// 鱼骨图分析数据
const fishboneAnalysis = reactive({
  people: '',
  machine: '',
  material: '',
  method: '',
  environment: '',
  measurement: ''
})

// 表单验证规则
const rules: FormRules = {
  problemId: [
    { required: true, message: '请选择问题', trigger: 'change' }
  ],
  analysisMethod: [
    { required: true, message: '请选择分析方法', trigger: 'change' }
  ],
  rootCause: [
    { required: true, message: '请输入根本原因', trigger: 'blur' }
  ],
  rootCauseType: [
    { required: true, message: '请选择根因类型', trigger: 'change' }
  ],
  analyst: [
    { required: true, message: '请输入分析人员', trigger: 'blur' }
  ]
}

// 问题选项
const problemOptions = ref([
  { id: 'LOP20250101001', description: '螺母孔径偏小' },
  { id: 'LOP20250101002', description: '焊接点虚焊' },
  { id: 'LOP20250101004', description: '设备温度异常' }
])

// 历史问题
const historyProblems = ref([
  { id: 'LOP20241201001', description: '类似的孔径问题' },
  { id: 'LOP20241215003', description: '焊接质量问题' },
  { id: 'LOP20241220005', description: '设备温控问题' }
])

// 分析列表数据
const analysisList = ref([
  {
    problemId: 'LOP20250101001',
    problemDesc: '螺母孔径偏小（实测6.93mm, 标准7±0.05mm,偏小0.02mm）',
    analysisMethod: '5Why分析',
    rootCause: '设备偏移→校准失效→点检漏项',
    analyst: '张工程师',
    createTime: '2025-01-01 10:30:00',
    status: '已完成'
  },
  {
    problemId: 'LOP20250101002',
    problemDesc: '焊接点虚焊导致接触不良',
    analysisMethod: '鱼骨图分析',
    rootCause: '焊接温度设定不当，操作员培训不足',
    analyst: '李工程师',
    createTime: '2025-01-01 11:15:00',
    status: '分析中'
  },
  {
    problemId: 'LOP20250101004',
    problemDesc: '设备温度异常波动',
    analysisMethod: '因果矩阵',
    rootCause: '温控传感器老化，PID参数未优化',
    analyst: '王工程师',
    createTime: '2025-01-01 15:20:00',
    status: '已完成'
  },
  {
    problemId: 'LOP20250101006',
    problemDesc: '产品表面划痕',
    analysisMethod: '5Why分析',
    rootCause: '传送带表面粗糙，维护周期过长',
    analyst: '赵工程师',
    createTime: '2025-01-01 16:45:00',
    status: '待分析'
  },
  {
    problemId: 'LOP20250101007',
    problemDesc: '包装密封不良',
    analysisMethod: '鱼骨图分析',
    rootCause: '密封条老化，更换标准不明确',
    analyst: '钱工程师',
    createTime: '2025-01-01 17:30:00',
    status: '已完成'
  }
])

// 方法
const getStatusType = (status: string) => {
  switch (status) {
    case '待分析': return 'warning'
    case '分析中': return 'primary'
    case '已完成': return 'success'
    default: return ''
  }
}

const handleSearch = () => {
  ElMessage.success('搜索功能演示')
}

const resetSearch = () => {
  Object.assign(searchForm, {
    problemId: '',
    status: ''
  })
}

const viewAnalysis = (row: any) => {
  ElMessage.info(`查看分析: ${row.problemId}`)
}

const editAnalysis = (row: any) => {
  isEdit.value = true
  Object.assign(analysisForm, row)
  showAnalysisDialog.value = true
}

const deleteAnalysis = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除分析 ${row.problemId} 吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    ElMessage.success('删除成功')
  })
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const addWhy = () => {
  if (whyAnalysis.value.length < 5) {
    whyAnalysis.value.push({ question: '', answer: '' })
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  isEdit.value = false
  whyAnalysis.value = [{ question: '', answer: '' }]
  Object.assign(fishboneAnalysis, {
    people: '',
    machine: '',
    material: '',
    method: '',
    environment: '',
    measurement: ''
  })
  Object.assign(analysisForm, {
    problemId: '',
    analysisMethod: '',
    rootCause: '',
    rootCauseType: '',
    relatedProblems: [],
    analyst: ''
  })
}

const submitForm = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      if (isEdit.value) {
        ElMessage.success('编辑成功')
      } else {
        ElMessage.success('新增成功')
      }
      showAnalysisDialog.value = false
      resetForm()
    }
  })
}

onMounted(() => {
  total.value = analysisList.value.length
})
</script>

<style scoped>
.root-cause-analysis {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.analysis-section {
  margin: 20px 0;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.analysis-section h4 {
  margin: 0 0 20px 0;
  color: #303133;
}

.why-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}
</style>
